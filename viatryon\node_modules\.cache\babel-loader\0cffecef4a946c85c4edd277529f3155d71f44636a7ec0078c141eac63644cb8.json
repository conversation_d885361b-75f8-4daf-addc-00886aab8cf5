{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\admin\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { Users, Eye, Globe, Activity } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _dashboardData$totalT, _dashboardData$tryOns, _dashboardData$active, _dashboardData$client, _dashboardData$active2, _uniqueUsers$summary, _uniqueUsers$summary$;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [deviceStats, setDeviceStats] = useState([]);\n  const [topClients, setTopClients] = useState([]);\n  const [uniqueUsers, setUniqueUsers] = useState(null);\n  const [businessMetrics, setBusinessMetrics] = useState(null);\n\n  // Fetch dashboard data from backend\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        var _uniqueUsersData, _uniqueUsersData$summ, _deviceAnalytics, _clientsData, _deviceAnalytics2;\n        setLoading(true);\n        setError(null);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        // Fetch overview analytics\n        const overviewResponse = await fetch(`${apiUrl}/api/analytics/admin/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!overviewResponse.ok) {\n          throw new Error('Failed to fetch overview data');\n        }\n        const overviewData = await overviewResponse.json();\n\n        // Fetch recent activity\n        const activityResponse = await fetch(`${apiUrl}/api/analytics/admin/recent-activity`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!activityResponse.ok) {\n          throw new Error('Failed to fetch recent activity');\n        }\n        const activityData = await activityResponse.json();\n\n        // Fetch unique users data\n        const uniqueUsersResponse = await fetch(`${apiUrl}/api/analytics/admin/unique-users?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        let uniqueUsersData = null;\n        if (uniqueUsersResponse.ok) {\n          uniqueUsersData = await uniqueUsersResponse.json();\n        }\n\n        // Fetch device analytics\n        const deviceResponse = await fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        let deviceAnalytics = null;\n        if (deviceResponse.ok) {\n          deviceAnalytics = await deviceResponse.json();\n        }\n\n        // Fetch client performance data\n        const clientsResponse = await fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        let clientsData = null;\n        if (clientsResponse.ok) {\n          clientsData = await clientsResponse.json();\n        }\n\n        // Set all the data\n        setDashboardData({\n          totalTryOns: overviewData.totalSessions || 0,\n          totalClients: overviewData.totalClients || 0,\n          activeClients: overviewData.activeClients || 0,\n          activeUsers: ((_uniqueUsersData = uniqueUsersData) === null || _uniqueUsersData === void 0 ? void 0 : (_uniqueUsersData$summ = _uniqueUsersData.summary) === null || _uniqueUsersData$summ === void 0 ? void 0 : _uniqueUsersData$summ.totalUniqueUsers) || 0,\n          trends: overviewData.trends || [],\n          tryOnsGrowth: overviewData.tryOnsGrowth || 0,\n          clientsGrowth: overviewData.clientsGrowth || 0,\n          usersGrowth: overviewData.usersGrowth || 0\n        });\n\n        // Transform device data from backend - use device analytics if available, otherwise fallback to overview\n        const deviceData = ((_deviceAnalytics = deviceAnalytics) === null || _deviceAnalytics === void 0 ? void 0 : _deviceAnalytics.devices) || overviewData.deviceStats || [];\n        const totalDeviceSessions = deviceData.reduce((sum, device) => sum + (device.count || device.sessions || 0), 0);\n        const deviceColors = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\n        setDeviceStats(deviceData.map((device, index) => {\n          var _ref, _ref2;\n          return {\n            name: ((_ref = device.device || device._id || 'Unknown') === null || _ref === void 0 ? void 0 : _ref.charAt(0).toUpperCase()) + ((_ref2 = device.device || device._id || 'Unknown') === null || _ref2 === void 0 ? void 0 : _ref2.slice(1)),\n            value: totalDeviceSessions > 0 ? Math.round((device.count || device.sessions || 0) / totalDeviceSessions * 100) : 0,\n            color: deviceColors[index % deviceColors.length]\n          };\n        }));\n\n        // Set client data - use clients analytics if available, otherwise fallback to overview\n        setTopClients(((_clientsData = clientsData) === null || _clientsData === void 0 ? void 0 : _clientsData.clients) || overviewData.topClients || []);\n        setRecentActivity(activityData.activities || []);\n        setUniqueUsers(uniqueUsersData);\n        setBusinessMetrics(((_deviceAnalytics2 = deviceAnalytics) === null || _deviceAnalytics2 === void 0 ? void 0 : _deviceAnalytics2.metrics) || null);\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError(err.message);\n        // Set empty data instead of mockup data\n        setDashboardData({\n          totalTryOns: 0,\n          totalClients: 0,\n          activeClients: 0,\n          activeUsers: 0,\n          trends: [],\n          tryOnsGrowth: 0,\n          clientsGrowth: 0,\n          usersGrowth: 0\n        });\n        setDeviceStats([]);\n        setTopClients([]);\n        setRecentActivity([]);\n        setUniqueUsers(null);\n        setBusinessMetrics(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDashboardData();\n  }, [timeRange]);\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Remove mockup data\n  const tryOnTrends = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.trends) || [];\n  const clientPerformance = topClients || [];\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n        isOpen: isSidebarOpen,\n        onClose: () => setIsSidebarOpen(false),\n        collapsed: collapsed,\n        setCollapsed: setCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n        toggleSidebar: toggleSidebar,\n        collapsed: collapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `${mainMargin} pt-16 transition-all duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 md:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error && !dashboardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n        isOpen: isSidebarOpen,\n        onClose: () => setIsSidebarOpen(false),\n        collapsed: collapsed,\n        setCollapsed: setCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n        toggleSidebar: toggleSidebar,\n        collapsed: collapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `${mainMargin} pt-16 transition-all duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 md:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-red-800\",\n                  children: \"Error loading dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-sm text-red-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Virtual Try-On Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Monitor your platform performance and client success metrics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 md:mt-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n              children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setTimeRange(range),\n                className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n                children: range\n              }, range, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$totalT = dashboardData.totalTryOns) === null || _dashboardData$totalT === void 0 ? void 0 : _dashboardData$totalT.toLocaleString()) || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${(dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.tryOnsGrowth) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [(dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.tryOnsGrowth) >= 0 ? '+' : '', (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$tryOns = dashboardData.tryOnsGrowth) === null || _dashboardData$tryOns === void 0 ? void 0 : _dashboardData$tryOns.toFixed(1)) || '0', \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$active = dashboardData.activeClients) === null || _dashboardData$active === void 0 ? void 0 : _dashboardData$active.toLocaleString()) || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${(dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.clientsGrowth) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [(dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.clientsGrowth) >= 0 ? '+' : '', (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$client = dashboardData.clientsGrowth) === null || _dashboardData$client === void 0 ? void 0 : _dashboardData$client.toFixed(1)) || '0', \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users (by IP)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$active2 = dashboardData.activeUsers) === null || _dashboardData$active2 === void 0 ? void 0 : _dashboardData$active2.toLocaleString()) || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Globe, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: [\"Avg \", (uniqueUsers === null || uniqueUsers === void 0 ? void 0 : (_uniqueUsers$summary = uniqueUsers.summary) === null || _uniqueUsers$summary === void 0 ? void 0 : (_uniqueUsers$summary$ = _uniqueUsers$summary.avgSessionsPerUser) === null || _uniqueUsers$summary$ === void 0 ? void 0 : _uniqueUsers$summary$.toFixed(1)) || '0', \" sessions/user\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.5\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Try-On Trends\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: tryOnTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"tryOns\",\n                    stroke: \"#2D8C88\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#2D8C88'\n                    },\n                    name: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"conversions\",\n                    stroke: \"#3B82F6\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#3B82F6'\n                    },\n                    name: \"Conversions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Device Distribution\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: deviceStats.length > 0 ? /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: deviceStats,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    children: deviceStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: entry.color\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    formatter: value => [`${value}%`, 'Percentage'],\n                    labelFormatter: label => `${label} Devices`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500\",\n                  children: \"No device data available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 grid grid-cols-3 gap-4\",\n              children: deviceStats.map((device, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 rounded-full\",\n                    style: {\n                      backgroundColor: device.color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: device.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg font-semibold text-gray-900 mt-1\",\n                  children: [device.value, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.7\n          },\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Top Performing Clients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Avg Session Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: clientPerformance.length > 0 ? clientPerformance.map((client, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"hover:bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0 h-10 w-10\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                          children: (client.companyName || client.clientName || 'Unknown').charAt(0)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 453,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: client.companyName || client.clientName || 'Unknown Client'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 458,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [\"ID: \", client.clientId || client._id || 'N/A', client.productType && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"ml-2\",\n                            children: [\"\\u2022 \", client.productType]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 462,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 457,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: (client.totalTryOns || client.sessions || 0).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                    children: client.avgDuration ? `${Math.round(client.avgDuration / 1000)}s` : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                      children: client.isActive ? 'Active' : 'Inactive'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"4\",\n                    className: \"px-6 py-4 text-center text-sm text-gray-500\",\n                    children: \"No client performance data available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"divide-y divide-gray-200\",\n            children: recentActivity.length > 0 ? recentActivity.map((activity, index) => {\n              var _activity$action, _activity$action2, _activity$action3;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                    children: [(activity.type === 'new_client' || ((_activity$action = activity.action) === null || _activity$action === void 0 ? void 0 : _activity$action.includes('client'))) && /*#__PURE__*/_jsxDEV(Users, {\n                      className: \"h-6 w-6 text-[#2D8C88]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 99\n                    }, this), (activity.type === 'high_volume' || ((_activity$action2 = activity.action) === null || _activity$action2 === void 0 ? void 0 : _activity$action2.includes('session'))) && /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-6 w-6 text-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 101\n                    }, this), (activity.type === 'conversion' || ((_activity$action3 = activity.action) === null || _activity$action3 === void 0 ? void 0 : _activity$action3.includes('conversion'))) && /*#__PURE__*/_jsxDEV(Activity, {\n                      className: \"h-6 w-6 text-green-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 103\n                    }, this), !activity.type && !activity.action && /*#__PURE__*/_jsxDEV(Activity, {\n                      className: \"h-6 w-6 text-gray-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 62\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: activity.title || activity.action || 'Unknown activity'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: activity.description || activity.user || 'No description'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-auto\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: activity.timestamp || activity.time || 'Unknown time'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this);\n            }) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 text-center text-sm text-gray-500\",\n              children: \"No recent activity available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 232,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"WAvFlMgK+qRoemi2Zuap/Df4XUI=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Users", "Eye", "Globe", "Activity", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_dashboardData$totalT", "_dashboardData$tryOns", "_dashboardData$active", "_dashboardData$client", "_dashboardData$active2", "_uniqueUsers$summary", "_uniqueUsers$summary$", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "timeRange", "setTimeRange", "dashboardData", "setDashboardData", "loading", "setLoading", "error", "setError", "recentActivity", "setRecentActivity", "deviceStats", "setDeviceStats", "topClients", "setTopClients", "uniqueUsers", "setUniqueUsers", "businessMetrics", "setBusinessMetrics", "fetchDashboardData", "_uniqueUsersData", "_uniqueUsersData$summ", "_deviceAnalytics", "_clientsData", "_deviceAnalytics2", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "end", "Date", "start", "setDate", "getDate", "setFullYear", "getFullYear", "overviewResponse", "fetch", "toISOString", "headers", "ok", "overviewData", "json", "activityResponse", "activityData", "uniqueUsersResponse", "uniqueUsersData", "deviceResponse", "deviceAnalytics", "clientsResponse", "clientsData", "totalTryOns", "totalSessions", "totalClients", "activeClients", "activeUsers", "summary", "totalUniqueUsers", "trends", "tryOnsGrowth", "clientsGrowth", "usersGrowth", "deviceData", "devices", "totalDeviceSessions", "reduce", "sum", "device", "count", "sessions", "deviceColors", "map", "index", "_ref", "_ref2", "name", "_id", "char<PERSON>t", "toUpperCase", "value", "Math", "round", "color", "length", "clients", "activities", "metrics", "err", "console", "message", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "tryOnTrends", "clientPerformance", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "range", "onClick", "div", "initial", "opacity", "y", "animate", "toLocaleString", "toFixed", "transition", "delay", "avgSessionsPerUser", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "entry", "formatter", "labelFormatter", "style", "backgroundColor", "client", "companyName", "clientName", "clientId", "productType", "avgDuration", "isActive", "colSpan", "activity", "_activity$action", "_activity$action2", "_activity$action3", "action", "includes", "title", "description", "user", "timestamp", "time", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/admin/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { Users, Eye, Globe, Activity } from 'lucide-react';\n\nconst AdminDashboard = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [deviceStats, setDeviceStats] = useState([]);\n  const [topClients, setTopClients] = useState([]);\n  const [uniqueUsers, setUniqueUsers] = useState(null);\n  const [businessMetrics, setBusinessMetrics] = useState(null);\n\n  // Fetch dashboard data from backend\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        // Fetch overview analytics\n        const overviewResponse = await fetch(`${apiUrl}/api/analytics/admin/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!overviewResponse.ok) {\n          throw new Error('Failed to fetch overview data');\n        }\n\n        const overviewData = await overviewResponse.json();\n\n        // Fetch recent activity\n        const activityResponse = await fetch(`${apiUrl}/api/analytics/admin/recent-activity`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!activityResponse.ok) {\n          throw new Error('Failed to fetch recent activity');\n        }\n\n        const activityData = await activityResponse.json();\n\n        // Fetch unique users data\n        const uniqueUsersResponse = await fetch(`${apiUrl}/api/analytics/admin/unique-users?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        let uniqueUsersData = null;\n        if (uniqueUsersResponse.ok) {\n          uniqueUsersData = await uniqueUsersResponse.json();\n        }\n\n        // Fetch device analytics\n        const deviceResponse = await fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        let deviceAnalytics = null;\n        if (deviceResponse.ok) {\n          deviceAnalytics = await deviceResponse.json();\n        }\n\n        // Fetch client performance data\n        const clientsResponse = await fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        let clientsData = null;\n        if (clientsResponse.ok) {\n          clientsData = await clientsResponse.json();\n        }\n\n        // Set all the data\n        setDashboardData({\n          totalTryOns: overviewData.totalSessions || 0,\n          totalClients: overviewData.totalClients || 0,\n          activeClients: overviewData.activeClients || 0,\n          activeUsers: uniqueUsersData?.summary?.totalUniqueUsers || 0,\n          trends: overviewData.trends || [],\n          tryOnsGrowth: overviewData.tryOnsGrowth || 0,\n          clientsGrowth: overviewData.clientsGrowth || 0,\n          usersGrowth: overviewData.usersGrowth || 0\n        });\n\n        // Transform device data from backend - use device analytics if available, otherwise fallback to overview\n        const deviceData = deviceAnalytics?.devices || overviewData.deviceStats || [];\n        const totalDeviceSessions = deviceData.reduce((sum, device) => sum + (device.count || device.sessions || 0), 0);\n        const deviceColors = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\n\n        setDeviceStats(deviceData.map((device, index) => ({\n          name: (device.device || device._id || 'Unknown')?.charAt(0).toUpperCase() + (device.device || device._id || 'Unknown')?.slice(1),\n          value: totalDeviceSessions > 0 ? Math.round(((device.count || device.sessions || 0) / totalDeviceSessions) * 100) : 0,\n          color: deviceColors[index % deviceColors.length]\n        })));\n\n        // Set client data - use clients analytics if available, otherwise fallback to overview\n        setTopClients(clientsData?.clients || overviewData.topClients || []);\n        setRecentActivity(activityData.activities || []);\n        setUniqueUsers(uniqueUsersData);\n        setBusinessMetrics(deviceAnalytics?.metrics || null);\n\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError(err.message);\n        // Set empty data instead of mockup data\n        setDashboardData({\n          totalTryOns: 0,\n          totalClients: 0,\n          activeClients: 0,\n          activeUsers: 0,\n          trends: [],\n          tryOnsGrowth: 0,\n          clientsGrowth: 0,\n          usersGrowth: 0\n        });\n        setDeviceStats([]);\n        setTopClients([]);\n        setRecentActivity([]);\n        setUniqueUsers(null);\n        setBusinessMetrics(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, [timeRange]);\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Remove mockup data\n  const tryOnTrends = dashboardData?.trends || [];\n  const clientPerformance = topClients || [];\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n        <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n        <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n          <div className=\"p-4 md:p-6\">\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]\"></div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error && !dashboardData) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n        <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n        <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n          <div className=\"p-4 md:p-6\">\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-red-800\">Error loading dashboard</h3>\n                  <div className=\"mt-2 text-sm text-red-700\">\n                    <p>{error}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Virtual Try-On Analytics</h1>\n              <p className=\"text-gray-600\">Monitor your platform performance and client success metrics</p>\n            </div>\n            <div className=\"mt-4 md:mt-0\">\n              <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n                {['7d', '30d', '90d', '1y'].map((range) => (\n                  <button\n                    key={range}\n                    onClick={() => setTimeRange(range)}\n                    className={`px-3 py-1 text-sm font-medium rounded-md ${\n                      timeRange === range\n                        ? 'bg-[#2D8C88] text-white'\n                        : 'text-gray-600 hover:text-gray-900'\n                    }`}\n                  >\n                    {range}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Stats Grid */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6\">\n            {/* Total Try-Ons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{dashboardData?.totalTryOns?.toLocaleString() || '0'}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${dashboardData?.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {dashboardData?.tryOnsGrowth >= 0 ? '+' : ''}{dashboardData?.tryOnsGrowth?.toFixed(1) || '0'}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">from last week</span>\n              </div>\n            </motion.div>\n\n            {/* Active Clients */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{dashboardData?.activeClients?.toLocaleString() || '0'}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${dashboardData?.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {dashboardData?.clientsGrowth >= 0 ? '+' : ''}{dashboardData?.clientsGrowth?.toFixed(1) || '0'}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\n              </div>\n            </motion.div>\n\n            {/* Unique Users */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users (by IP)</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{dashboardData?.activeUsers?.toLocaleString() || '0'}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\n                  <Globe className=\"h-6 w-6 text-purple-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-gray-600\">\n                  Avg {uniqueUsers?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} sessions/user\n                </span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Charts Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n            {/* Try-On Trends */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Try-On Trends</h3>\n              <div className=\"h-80\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <LineChart data={tryOnTrends}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"date\" />\n                    <YAxis />\n                    <Tooltip />\n                    <Line\n                      type=\"monotone\"\n                      dataKey=\"tryOns\"\n                      stroke=\"#2D8C88\"\n                      strokeWidth={2}\n                      dot={{ fill: '#2D8C88' }}\n                      name=\"Try-Ons\"\n                    />\n                    <Line\n                      type=\"monotone\"\n                      dataKey=\"conversions\"\n                      stroke=\"#3B82F6\"\n                      strokeWidth={2}\n                      dot={{ fill: '#3B82F6' }}\n                      name=\"Conversions\"\n                    />\n                  </LineChart>\n                </ResponsiveContainer>\n              </div>\n            </motion.div>\n\n            {/* Device Distribution */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Distribution</h3>\n              <div className=\"h-80\">\n                {deviceStats.length > 0 ? (\n                  <ResponsiveContainer width=\"100%\" height=\"100%\">\n                    <PieChart>\n                      <Pie\n                        data={deviceStats}\n                        cx=\"50%\"\n                        cy=\"50%\"\n                        labelLine={false}\n                        outerRadius={80}\n                        fill=\"#8884d8\"\n                        dataKey=\"value\"\n                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                      >\n                        {deviceStats.map((entry, index) => (\n                          <Cell key={`cell-${index}`} fill={entry.color} />\n                        ))}\n                      </Pie>\n                      <Tooltip \n                        formatter={(value) => [`${value}%`, 'Percentage']}\n                        labelFormatter={(label) => `${label} Devices`}\n                      />\n                    </PieChart>\n                  </ResponsiveContainer>\n                ) : (\n                  <div className=\"h-full flex items-center justify-center\">\n                    <p className=\"text-gray-500\">No device data available</p>\n                  </div>\n                )}\n              </div>\n              <div className=\"mt-4 grid grid-cols-3 gap-4\">\n                {deviceStats.map((device, index) => (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"flex items-center justify-center space-x-2\">\n                      <div className=\"w-3 h-3 rounded-full\" style={{ backgroundColor: device.color }}></div>\n                      <span className=\"text-sm font-medium text-gray-900\">{device.name}</span>\n                    </div>\n                    <p className=\"text-lg font-semibold text-gray-900 mt-1\">{device.value}%</p>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Top Performing Clients */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.7 }}\n            className=\"bg-white rounded-xl shadow-sm overflow-hidden mb-6\"\n          >\n            <div className=\"p-6 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Top Performing Clients</h2>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Try-Ons</th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Avg Session Duration</th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {clientPerformance.length > 0 ? clientPerformance.map((client, index) => (\n                    <tr key={index} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                              {(client.companyName || client.clientName || 'Unknown').charAt(0)}\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{client.companyName || client.clientName || 'Unknown Client'}</div>\n                            <div className=\"text-xs text-gray-500\">\n                              ID: {client.clientId || client._id || 'N/A'}\n                              {client.productType && (\n                                <span className=\"ml-2\">• {client.productType}</span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {(client.totalTryOns || client.sessions || 0).toLocaleString()}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {client.avgDuration ? `${Math.round(client.avgDuration / 1000)}s` : 'N/A'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          client.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n                        }`}>\n                          {client.isActive ? 'Active' : 'Inactive'}\n                        </span>\n                      </td>\n                    </tr>\n                  )) : (\n                    <tr>\n                      <td colSpan=\"4\" className=\"px-6 py-4 text-center text-sm text-gray-500\">\n                        No client performance data available\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </motion.div>\n\n          {/* Recent Activity */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8 }}\n            className=\"bg-white rounded-xl shadow-sm overflow-hidden\"\n          >\n            <div className=\"p-6 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Recent Activity</h2>\n            </div>\n            <div className=\"divide-y divide-gray-200\">\n              {recentActivity.length > 0 ? recentActivity.map((activity, index) => (\n                <div key={index} className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                      {(activity.type === 'new_client' || activity.action?.includes('client')) && <Users className=\"h-6 w-6 text-[#2D8C88]\" />}\n                      {(activity.type === 'high_volume' || activity.action?.includes('session')) && <Eye className=\"h-6 w-6 text-blue-500\" />}\n                      {(activity.type === 'conversion' || activity.action?.includes('conversion')) && <Activity className=\"h-6 w-6 text-green-500\" />}\n                      {!activity.type && !activity.action && <Activity className=\"h-6 w-6 text-gray-500\" />}\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{activity.title || activity.action || 'Unknown activity'}</p>\n                      <p className=\"text-sm text-gray-500\">{activity.description || activity.user || 'No description'}</p>\n                    </div>\n                    <div className=\"ml-auto\">\n                      <p className=\"text-sm text-gray-500\">{activity.timestamp || activity.time || 'Unknown time'}</p>\n                    </div>\n                  </div>\n                </div>\n              )) : (\n                <div className=\"p-6 text-center text-sm text-gray-500\">\n                  No recent activity available\n                </div>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default AdminDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACzI,SAASC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,qBAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,YAAA,EAAAC,iBAAA;QACFlB,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMiB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;QAErE;QACA,MAAMO,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,IAAIC,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,QAAQpC,SAAS;UACf,KAAK,IAAI;YACPqC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,IAAI;YACPF,KAAK,CAACG,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C;UACF;YACEJ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC;;QAEA;QACA,MAAMG,gBAAgB,GAAG,MAAMC,KAAK,CAAC,GAAGX,MAAM,uCAAuCK,KAAK,CAACO,WAAW,CAAC,CAAC,QAAQT,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,EAAE;UACnIC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACkB,gBAAgB,CAACI,EAAE,EAAE;UACxB,MAAM,IAAInB,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,MAAMoB,YAAY,GAAG,MAAML,gBAAgB,CAACM,IAAI,CAAC,CAAC;;QAElD;QACA,MAAMC,gBAAgB,GAAG,MAAMN,KAAK,CAAC,GAAGX,MAAM,sCAAsC,EAAE;UACpFa,OAAO,EAAE;YACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACyB,gBAAgB,CAACH,EAAE,EAAE;UACxB,MAAM,IAAInB,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEA,MAAMuB,YAAY,GAAG,MAAMD,gBAAgB,CAACD,IAAI,CAAC,CAAC;;QAElD;QACA,MAAMG,mBAAmB,GAAG,MAAMR,KAAK,CAAC,GAAGX,MAAM,2CAA2CK,KAAK,CAACO,WAAW,CAAC,CAAC,QAAQT,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,EAAE;UAC1IC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI4B,eAAe,GAAG,IAAI;QAC1B,IAAID,mBAAmB,CAACL,EAAE,EAAE;UAC1BM,eAAe,GAAG,MAAMD,mBAAmB,CAACH,IAAI,CAAC,CAAC;QACpD;;QAEA;QACA,MAAMK,cAAc,GAAG,MAAMV,KAAK,CAAC,GAAGX,MAAM,sCAAsCK,KAAK,CAACO,WAAW,CAAC,CAAC,QAAQT,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,EAAE;UAChIC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI8B,eAAe,GAAG,IAAI;QAC1B,IAAID,cAAc,CAACP,EAAE,EAAE;UACrBQ,eAAe,GAAG,MAAMD,cAAc,CAACL,IAAI,CAAC,CAAC;QAC/C;;QAEA;QACA,MAAMO,eAAe,GAAG,MAAMZ,KAAK,CAAC,GAAGX,MAAM,sCAAsCK,KAAK,CAACO,WAAW,CAAC,CAAC,QAAQT,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,EAAE;UACjIC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUrB,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIgC,WAAW,GAAG,IAAI;QACtB,IAAID,eAAe,CAACT,EAAE,EAAE;UACtBU,WAAW,GAAG,MAAMD,eAAe,CAACP,IAAI,CAAC,CAAC;QAC5C;;QAEA;QACA7C,gBAAgB,CAAC;UACfsD,WAAW,EAAEV,YAAY,CAACW,aAAa,IAAI,CAAC;UAC5CC,YAAY,EAAEZ,YAAY,CAACY,YAAY,IAAI,CAAC;UAC5CC,aAAa,EAAEb,YAAY,CAACa,aAAa,IAAI,CAAC;UAC9CC,WAAW,EAAE,EAAA1C,gBAAA,GAAAiC,eAAe,cAAAjC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB2C,OAAO,cAAA1C,qBAAA,uBAAxBA,qBAAA,CAA0B2C,gBAAgB,KAAI,CAAC;UAC5DC,MAAM,EAAEjB,YAAY,CAACiB,MAAM,IAAI,EAAE;UACjCC,YAAY,EAAElB,YAAY,CAACkB,YAAY,IAAI,CAAC;UAC5CC,aAAa,EAAEnB,YAAY,CAACmB,aAAa,IAAI,CAAC;UAC9CC,WAAW,EAAEpB,YAAY,CAACoB,WAAW,IAAI;QAC3C,CAAC,CAAC;;QAEF;QACA,MAAMC,UAAU,GAAG,EAAA/C,gBAAA,GAAAiC,eAAe,cAAAjC,gBAAA,uBAAfA,gBAAA,CAAiBgD,OAAO,KAAItB,YAAY,CAACrC,WAAW,IAAI,EAAE;QAC7E,MAAM4D,mBAAmB,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAID,MAAM,CAACE,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/G,MAAMC,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAE5EjE,cAAc,CAACyD,UAAU,CAACS,GAAG,CAAC,CAACJ,MAAM,EAAEK,KAAK;UAAA,IAAAC,IAAA,EAAAC,KAAA;UAAA,OAAM;YAChDC,IAAI,EAAE,EAAAF,IAAA,GAACN,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACS,GAAG,IAAI,SAAS,cAAAH,IAAA,uBAAzCA,IAAA,CAA4CI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,MAAAJ,KAAA,GAAIP,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACS,GAAG,IAAI,SAAS,cAAAF,KAAA,uBAAzCA,KAAA,CAA4C9C,KAAK,CAAC,CAAC,CAAC;YAChImD,KAAK,EAAEf,mBAAmB,GAAG,CAAC,GAAGgB,IAAI,CAACC,KAAK,CAAE,CAACd,MAAM,CAACC,KAAK,IAAID,MAAM,CAACE,QAAQ,IAAI,CAAC,IAAIL,mBAAmB,GAAI,GAAG,CAAC,GAAG,CAAC;YACrHkB,KAAK,EAAEZ,YAAY,CAACE,KAAK,GAAGF,YAAY,CAACa,MAAM;UACjD,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA5E,aAAa,CAAC,EAAAS,YAAA,GAAAkC,WAAW,cAAAlC,YAAA,uBAAXA,YAAA,CAAaoE,OAAO,KAAI3C,YAAY,CAACnC,UAAU,IAAI,EAAE,CAAC;QACpEH,iBAAiB,CAACyC,YAAY,CAACyC,UAAU,IAAI,EAAE,CAAC;QAChD5E,cAAc,CAACqC,eAAe,CAAC;QAC/BnC,kBAAkB,CAAC,EAAAM,iBAAA,GAAA+B,eAAe,cAAA/B,iBAAA,uBAAfA,iBAAA,CAAiBqE,OAAO,KAAI,IAAI,CAAC;MAEtD,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACxF,KAAK,CAAC,gCAAgC,EAAEuF,GAAG,CAAC;QACpDtF,QAAQ,CAACsF,GAAG,CAACE,OAAO,CAAC;QACrB;QACA5F,gBAAgB,CAAC;UACfsD,WAAW,EAAE,CAAC;UACdE,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,CAAC;UAChBC,WAAW,EAAE,CAAC;UACdG,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,CAAC;UAChBC,WAAW,EAAE;QACf,CAAC,CAAC;QACFxD,cAAc,CAAC,EAAE,CAAC;QAClBE,aAAa,CAAC,EAAE,CAAC;QACjBJ,iBAAiB,CAAC,EAAE,CAAC;QACrBM,cAAc,CAAC,IAAI,CAAC;QACpBE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,SAAS;QACRZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDa,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAClB,SAAS,CAAC,CAAC;EAEf,MAAMgG,aAAa,GAAGA,CAAA,KAAM;IAC1BnG,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMqG,UAAU,GAAGnG,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAMoG,WAAW,GAAG,CAAAhG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,MAAM,KAAI,EAAE;EAC/C,MAAMmC,iBAAiB,GAAGvF,UAAU,IAAI,EAAE;;EAE1C;EACA,IAAIR,OAAO,EAAE;IACX,oBACElB,OAAA;MAAKkH,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCnH,OAAA,CAACpB,YAAY;QAACwI,MAAM,EAAE1G,aAAc;QAAC2G,OAAO,EAAEA,CAAA,KAAM1G,gBAAgB,CAAC,KAAK,CAAE;QAACC,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjIzH,OAAA,CAACnB,WAAW;QAACiI,aAAa,EAAEA,aAAc;QAAClG,SAAS,EAAEA;MAAU;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEzH,OAAA;QAAMkH,SAAS,EAAE,GAAGH,UAAU,oCAAqC;QAAAI,QAAA,eACjEnH,OAAA;UAAKkH,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBnH,OAAA;YAAKkH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDnH,OAAA;cAAKkH,SAAS,EAAC;YAAiE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;;EAEA;EACA,IAAIrG,KAAK,IAAI,CAACJ,aAAa,EAAE;IAC3B,oBACEhB,OAAA;MAAKkH,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCnH,OAAA,CAACpB,YAAY;QAACwI,MAAM,EAAE1G,aAAc;QAAC2G,OAAO,EAAEA,CAAA,KAAM1G,gBAAgB,CAAC,KAAK,CAAE;QAACC,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjIzH,OAAA,CAACnB,WAAW;QAACiI,aAAa,EAAEA,aAAc;QAAClG,SAAS,EAAEA;MAAU;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnEzH,OAAA;QAAMkH,SAAS,EAAE,GAAGH,UAAU,oCAAqC;QAAAI,QAAA,eACjEnH,OAAA;UAAKkH,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBnH,OAAA;YAAKkH,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DnH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnH,OAAA;gBAAKkH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnH,OAAA;kBAAIkH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EzH,OAAA;kBAAKkH,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxCnH,OAAA;oBAAAmH,QAAA,EAAI/F;kBAAK;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEzH,OAAA;IAAKkH,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCnH,OAAA,CAACpB,YAAY;MAACwI,MAAM,EAAE1G,aAAc;MAAC2G,OAAO,EAAEA,CAAA,KAAM1G,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjIzH,OAAA,CAACnB,WAAW;MAACiI,aAAa,EAAEA,aAAc;MAAClG,SAAS,EAAEA;IAAU;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEzH,OAAA;MAAMkH,SAAS,EAAE,GAAGH,UAAU,oCAAqC;MAAAI,QAAA,eACjEnH,OAAA;QAAKkH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBnH,OAAA;UAAKkH,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFnH,OAAA;YAAAmH,QAAA,gBACEnH,OAAA;cAAIkH,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EzH,OAAA;cAAGkH,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eACNzH,OAAA;YAAKkH,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BnH,OAAA;cAAKkH,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACxB,GAAG,CAAE+B,KAAK,iBACpC1H,OAAA;gBAEE2H,OAAO,EAAEA,CAAA,KAAM5G,YAAY,CAAC2G,KAAK,CAAE;gBACnCR,SAAS,EAAE,4CACTpG,SAAS,KAAK4G,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;gBAAAP,QAAA,EAEFO;cAAK,GARDA,KAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzH,OAAA;UAAKkH,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBAEjFnH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9Bb,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnH,OAAA;cAAKkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnH,OAAA;gBAAAmH,QAAA,gBACEnH,OAAA;kBAAGkH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEzH,OAAA;kBAAGkH,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE,CAAAnG,aAAa,aAAbA,aAAa,wBAAAb,qBAAA,GAAba,aAAa,CAAEuD,WAAW,cAAApE,qBAAA,uBAA1BA,qBAAA,CAA4B8H,cAAc,CAAC,CAAC,KAAI;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACNzH,OAAA;gBAAKkH,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFnH,OAAA,CAACJ,GAAG;kBAACsH,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnH,OAAA;gBAAMkH,SAAS,EAAE,uBAAuB,CAAAlG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+D,YAAY,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAoC,QAAA,GAC5G,CAAAnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+D,YAAY,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAA/D,aAAa,aAAbA,aAAa,wBAAAZ,qBAAA,GAAbY,aAAa,CAAE+D,YAAY,cAAA3E,qBAAA,uBAA3BA,qBAAA,CAA6B8H,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,GAC/F;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPzH,OAAA;gBAAMkH,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnH,OAAA;cAAKkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnH,OAAA;gBAAAmH,QAAA,gBACEnH,OAAA;kBAAGkH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEzH,OAAA;kBAAGkH,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE,CAAAnG,aAAa,aAAbA,aAAa,wBAAAX,qBAAA,GAAbW,aAAa,CAAE0D,aAAa,cAAArE,qBAAA,uBAA5BA,qBAAA,CAA8B4H,cAAc,CAAC,CAAC,KAAI;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC,eACNzH,OAAA;gBAAKkH,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFnH,OAAA,CAACL,KAAK;kBAACuH,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnH,OAAA;gBAAMkH,SAAS,EAAE,uBAAuB,CAAAlG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgE,aAAa,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAmC,QAAA,GAC7G,CAAAnG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgE,aAAa,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAAhE,aAAa,aAAbA,aAAa,wBAAAV,qBAAA,GAAbU,aAAa,CAAEgE,aAAa,cAAA1E,qBAAA,uBAA5BA,qBAAA,CAA8B4H,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,GACjG;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPzH,OAAA;gBAAMkH,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnH,OAAA;cAAKkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnH,OAAA;gBAAAmH,QAAA,gBACEnH,OAAA;kBAAGkH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzEzH,OAAA;kBAAGkH,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE,CAAAnG,aAAa,aAAbA,aAAa,wBAAAT,sBAAA,GAAbS,aAAa,CAAE2D,WAAW,cAAApE,sBAAA,uBAA1BA,sBAAA,CAA4B0H,cAAc,CAAC,CAAC,KAAI;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACNzH,OAAA;gBAAKkH,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFnH,OAAA,CAACH,KAAK;kBAACqH,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnH,OAAA;gBAAMkH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,MAC9C,EAAC,CAAAvF,WAAW,aAAXA,WAAW,wBAAApB,oBAAA,GAAXoB,WAAW,CAAEgD,OAAO,cAAApE,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsB6H,kBAAkB,cAAA5H,qBAAA,uBAAxCA,qBAAA,CAA0CyH,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,gBACnE;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNzH,OAAA;UAAKkH,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzDnH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnH,OAAA;cAAIkH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEzH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnH,OAAA,CAACT,mBAAmB;gBAAC+I,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAApB,QAAA,eAC7CnH,OAAA,CAACjB,SAAS;kBAACyJ,IAAI,EAAExB,WAAY;kBAAAG,QAAA,gBAC3BnH,OAAA,CAACX,aAAa;oBAACoJ,eAAe,EAAC;kBAAK;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCzH,OAAA,CAACb,KAAK;oBAACuJ,OAAO,EAAC;kBAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBzH,OAAA,CAACZ,KAAK;oBAAAkI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTzH,OAAA,CAACV,OAAO;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXzH,OAAA,CAAChB,IAAI;oBACH2J,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,QAAQ;oBAChBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBhD,IAAI,EAAC;kBAAS;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFzH,OAAA,CAAChB,IAAI;oBACH2J,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,aAAa;oBACrBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBhD,IAAI,EAAC;kBAAa;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnH,OAAA;cAAIkH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EzH,OAAA;cAAKkH,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClB3F,WAAW,CAAC+E,MAAM,GAAG,CAAC,gBACrBvG,OAAA,CAACT,mBAAmB;gBAAC+I,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAApB,QAAA,eAC7CnH,OAAA,CAACR,QAAQ;kBAAA2H,QAAA,gBACPnH,OAAA,CAACP,GAAG;oBACF+I,IAAI,EAAEhH,WAAY;oBAClBwH,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBC,WAAW,EAAE,EAAG;oBAChBJ,IAAI,EAAC,SAAS;oBACdL,OAAO,EAAC,OAAO;oBACfU,KAAK,EAAEA,CAAC;sBAAErD,IAAI;sBAAEsD;oBAAQ,CAAC,KAAK,GAAGtD,IAAI,IAAI,CAACsD,OAAO,GAAG,GAAG,EAAEnB,OAAO,CAAC,CAAC,CAAC,GAAI;oBAAAf,QAAA,EAEtE3F,WAAW,CAACmE,GAAG,CAAC,CAAC2D,KAAK,EAAE1D,KAAK,kBAC5B5F,OAAA,CAACN,IAAI;sBAAuBqJ,IAAI,EAAEO,KAAK,CAAChD;oBAAM,GAAnC,QAAQV,KAAK,EAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAsB,CACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNzH,OAAA,CAACV,OAAO;oBACNiK,SAAS,EAAGpD,KAAK,IAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,YAAY,CAAE;oBAClDqD,cAAc,EAAGJ,KAAK,IAAK,GAAGA,KAAK;kBAAW;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,gBAEtBzH,OAAA;gBAAKkH,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,eACtDnH,OAAA;kBAAGkH,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzH,OAAA;cAAKkH,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACzC3F,WAAW,CAACmE,GAAG,CAAC,CAACJ,MAAM,EAAEK,KAAK,kBAC7B5F,OAAA;gBAAiBkH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACtCnH,OAAA;kBAAKkH,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzDnH,OAAA;oBAAKkH,SAAS,EAAC,sBAAsB;oBAACuC,KAAK,EAAE;sBAAEC,eAAe,EAAEnE,MAAM,CAACe;oBAAM;kBAAE;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtFzH,OAAA;oBAAMkH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAE5B,MAAM,CAACQ;kBAAI;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNzH,OAAA;kBAAGkH,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAE5B,MAAM,CAACY,KAAK,EAAC,GAAC;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA,GALnE7B,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNzH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAE9DnH,OAAA;YAAKkH,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CnH,OAAA;cAAIkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNzH,OAAA;YAAKkH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BnH,OAAA;cAAOkH,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDnH,OAAA;gBAAOkH,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BnH,OAAA;kBAAAmH,QAAA,gBACEnH,OAAA;oBAAIkH,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GzH,OAAA;oBAAIkH,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3GzH,OAAA;oBAAIkH,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxHzH,OAAA;oBAAIkH,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRzH,OAAA;gBAAOkH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDF,iBAAiB,CAACV,MAAM,GAAG,CAAC,GAAGU,iBAAiB,CAACtB,GAAG,CAAC,CAACgE,MAAM,EAAE/D,KAAK,kBAClE5F,OAAA;kBAAgBkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC1CnH,OAAA;oBAAIkH,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzCnH,OAAA;sBAAKkH,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCnH,OAAA;wBAAKkH,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,eACtCnH,OAAA;0BAAKkH,SAAS,EAAC,iFAAiF;0BAAAC,QAAA,EAC7F,CAACwC,MAAM,CAACC,WAAW,IAAID,MAAM,CAACE,UAAU,IAAI,SAAS,EAAE5D,MAAM,CAAC,CAAC;wBAAC;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzH,OAAA;wBAAKkH,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBnH,OAAA;0BAAKkH,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEwC,MAAM,CAACC,WAAW,IAAID,MAAM,CAACE,UAAU,IAAI;wBAAgB;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtHzH,OAAA;0BAAKkH,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAC,MACjC,EAACwC,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAAC3D,GAAG,IAAI,KAAK,EAC1C2D,MAAM,CAACI,WAAW,iBACjB/J,OAAA;4BAAMkH,SAAS,EAAC,MAAM;4BAAAC,QAAA,GAAC,SAAE,EAACwC,MAAM,CAACI,WAAW;0BAAA;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CACpD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLzH,OAAA;oBAAIkH,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAC9D,CAACwC,MAAM,CAACpF,WAAW,IAAIoF,MAAM,CAAClE,QAAQ,IAAI,CAAC,EAAEwC,cAAc,CAAC;kBAAC;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACLzH,OAAA;oBAAIkH,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAC9DwC,MAAM,CAACK,WAAW,GAAG,GAAG5D,IAAI,CAACC,KAAK,CAACsD,MAAM,CAACK,WAAW,GAAG,IAAI,CAAC,GAAG,GAAG;kBAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACLzH,OAAA;oBAAIkH,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzCnH,OAAA;sBAAMkH,SAAS,EAAE,2EACfyC,MAAM,CAACM,QAAQ,GAAG,6BAA6B,GAAG,2BAA2B,EAC5E;sBAAA9C,QAAA,EACAwC,MAAM,CAACM,QAAQ,GAAG,QAAQ,GAAG;oBAAU;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GA/BE7B,KAAK;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCV,CACL,CAAC,gBACAzH,OAAA;kBAAAmH,QAAA,eACEnH,OAAA;oBAAIkK,OAAO,EAAC,GAAG;oBAAChD,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,EAAC;kBAExE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbzH,OAAA,CAAClB,MAAM,CAAC8I,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAEzDnH,OAAA;YAAKkH,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CnH,OAAA;cAAIkH,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACNzH,OAAA;YAAKkH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACtC7F,cAAc,CAACiF,MAAM,GAAG,CAAC,GAAGjF,cAAc,CAACqE,GAAG,CAAC,CAACwE,QAAQ,EAAEvE,KAAK;cAAA,IAAAwE,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;cAAA,oBAC9DtK,OAAA;gBAAiBkH,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAC9BnH,OAAA;kBAAKkH,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnH,OAAA;oBAAKkH,SAAS,EAAC,yEAAyE;oBAAAC,QAAA,GACrF,CAACgD,QAAQ,CAACxB,IAAI,KAAK,YAAY,MAAAyB,gBAAA,GAAID,QAAQ,CAACI,MAAM,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,QAAQ,CAAC,QAAQ,CAAC,mBAAKxK,OAAA,CAACL,KAAK;sBAACuH,SAAS,EAAC;oBAAwB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvH,CAAC0C,QAAQ,CAACxB,IAAI,KAAK,aAAa,MAAA0B,iBAAA,GAAIF,QAAQ,CAACI,MAAM,cAAAF,iBAAA,uBAAfA,iBAAA,CAAiBG,QAAQ,CAAC,SAAS,CAAC,mBAAKxK,OAAA,CAACJ,GAAG;sBAACsH,SAAS,EAAC;oBAAuB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtH,CAAC0C,QAAQ,CAACxB,IAAI,KAAK,YAAY,MAAA2B,iBAAA,GAAIH,QAAQ,CAACI,MAAM,cAAAD,iBAAA,uBAAfA,iBAAA,CAAiBE,QAAQ,CAAC,YAAY,CAAC,mBAAKxK,OAAA,CAACF,QAAQ;sBAACoH,SAAS,EAAC;oBAAwB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC9H,CAAC0C,QAAQ,CAACxB,IAAI,IAAI,CAACwB,QAAQ,CAACI,MAAM,iBAAIvK,OAAA,CAACF,QAAQ;sBAACoH,SAAS,EAAC;oBAAuB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNzH,OAAA;oBAAAmH,QAAA,gBACEnH,OAAA;sBAAGkH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEgD,QAAQ,CAACM,KAAK,IAAIN,QAAQ,CAACI,MAAM,IAAI;oBAAkB;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9GzH,OAAA;sBAAGkH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEgD,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACQ,IAAI,IAAI;oBAAgB;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC,eACNzH,OAAA;oBAAKkH,SAAS,EAAC,SAAS;oBAAAC,QAAA,eACtBnH,OAAA;sBAAGkH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEgD,QAAQ,CAACS,SAAS,IAAIT,QAAQ,CAACU,IAAI,IAAI;oBAAc;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAfE7B,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBV,CAAC;YAAA,CACP,CAAC,gBACAzH,OAAA;cAAKkH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEvD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvH,EAAA,CA9gBID,cAAc;AAAA6K,EAAA,GAAd7K,cAAc;AAghBpB,eAAeA,cAAc;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}