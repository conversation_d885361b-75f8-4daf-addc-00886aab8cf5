import React, { useState, useEffect } from 'react';
import Client<PERSON>idebar from '../../components/client/ClientSidebar';
import ClientNavbar from '../../components/client/ClientNavbar';
import EmbedCodeGenerator from '../../components/EmbedCodeGenerator';
import { motion } from 'framer-motion';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Eye, TrendingUp, Users, Clock, Code, Globe, Smartphone } from 'lucide-react';
import axios from 'axios';

const ClientDashboard = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [showEmbedModal, setShowEmbedModal] = useState(false);
  const [clientData, setClientData] = useState({
    id: '',
    totalTryOns: 0,
    avgDuration: 0,
    uniqueUsers: 0,
    companyName: '',
    email: '',
    productType: 'watches',
    productName: '',
    productImageUrl: '',
    caseDimensions: '42'
  });
  const [user, setUser] = useState(null);
  const [analyticsData, setAnalyticsData] = useState({
    trends: [],
    products: [],
    devices: [],
    overview: null,
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Load client data from localStorage and API
  useEffect(() => {
    const loadClientData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Fetch client profile
        const profileResponse = await axios.get(`${apiUrl}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (profileResponse.data) {
          // Extract client ID from MongoDB ObjectId (without 'object' prefix)
          const clientId = profileResponse.data._id || profileResponse.data.id || '';

          setUser(profileResponse.data);
          setClientData(prev => ({
            ...prev,
            id: clientId,
            companyName: profileResponse.data.companyName || '',
            email: profileResponse.data.email || '',
            productType: profileResponse.data.productType || 'watches',
            productName: profileResponse.data.productName || '',
            productImageUrl: profileResponse.data.productImageUrl || '',
            caseDimensions: profileResponse.data.caseDimensions || (profileResponse.data.productType === 'bracelets' ? '15' : '42')
          }));
        }

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch analytics data from multiple endpoints
        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([
          axios.get(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }),
          axios.get(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }),
          axios.get(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          })
        ]);

        if (timeAnalysisResponse.data) {
          const timeData = timeAnalysisResponse.data;
          const totalSessions = timeData.dailyTrends?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0;
          const avgDuration = timeData.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / 
            (timeData.dailyTrends?.length || 1);

          setClientData(prev => ({
            ...prev,
            totalTryOns: totalSessions,
            avgDuration: Math.round(avgDuration)
          }));

          setAnalyticsData(prev => ({
            ...prev,
            trends: timeData.dailyTrends || []
          }));
        }

        if (productPerformanceResponse.data) {
          setAnalyticsData(prev => ({
            ...prev,
            products: productPerformanceResponse.data
          }));
        }

        if (deviceStatsResponse.data) {
          const uniqueUsers = deviceStatsResponse.data.reduce((sum, device) => sum + (device.sessions || 0), 0);
          setClientData(prev => ({
            ...prev,
            uniqueUsers
          }));
          setAnalyticsData(prev => ({
            ...prev,
            devices: deviceStatsResponse.data
          }));
        }

      } catch (error) {
        console.error('Error loading client data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadClientData();
  }, [timeRange]);

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Format data for charts
  const tryOnTrends = analyticsData.trends?.map(trend => ({
    date: trend._id,
    tryOns: trend.sessions,
    avgDuration: Math.round(trend.avgDuration || 0)
  })) || [];

  const productPerformance = analyticsData.products?.slice(0, 5).map(product => ({
    name: product.productName || 'Unknown Product',
    tryOns: product.sessions,
    avgDuration: Math.round(product.avgDuration || 0)
  })) || [];

  const deviceStats = analyticsData.devices?.map((device, index) => ({
    name: device._id,
    value: device.sessions,
    color: ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 5]
  })) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
        <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

        <main className={`${mainMargin} pt-20 transition-all duration-300`}>
          <div className="p-4 md:p-6 space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>

              <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                {[1, 2].map((i) => (
                  <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                    <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="h-80 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-20 transition-all duration-300`}>
        <div className="p-4 md:p-6 space-y-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Virtual Try-On Dashboard</h1>
              <p className="text-gray-600">Monitor your product performance and customer engagement</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <div className="inline-flex rounded-lg border border-gray-200 p-1">
                {['7d', '30d', '90d', '1y'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-3 py-1 text-sm font-medium rounded-md ${
                      timeRange === range
                        ? 'bg-[#2D8C88] text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
              <button
                onClick={() => setShowEmbedModal(true)}
                className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
              >
                <Code className="h-4 w-4 mr-2" />
                Get Embed Code
              </button>
            </div>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
            {/* Total Try-Ons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{clientData.totalTryOns.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
            </motion.div>

            {/* Average Duration */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Duration</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{Math.floor(clientData.avgDuration / 60)}m {clientData.avgDuration % 60}s</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-blue-500" />
                </div>
              </div>
            </motion.div>

            {/* Unique Users */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{clientData.uniqueUsers.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-500" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Try-On Trends */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Try-On Trends</h3>
              <div className="h-64 md:h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={tryOnTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="tryOns"
                      stroke="#2D8C88"
                      strokeWidth={2}
                      dot={{ fill: '#2D8C88' }}
                      name="Try-Ons"
                    />
                    <Line
                      type="monotone"
                      dataKey="avgDuration"
                      stroke="#3B82F6"
                      strokeWidth={2}
                      dot={{ fill: '#3B82F6' }}
                      name="Avg Duration (s)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </motion.div>

            {/* Device Distribution */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Device Usage</h3>
              <div className="h-64 md:h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deviceStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {deviceStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </motion.div>
          </div>

          {/* Product Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Products</h3>
            <div className="h-64 md:h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={productPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="tryOns" fill="#2D8C88" name="Try-Ons" />
                  <Bar dataKey="avgDuration" fill="#3B82F6" name="Avg Duration (s)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>

          {/* Integration Guide */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between text-white">
              <div>
                <h3 className="text-lg font-medium">Ready to integrate Virtual Try-On?</h3>
                <p className="text-[#2D8C88]/80 mt-1">Add our try-on button to your product pages in minutes</p>
              </div>
              <div className="flex space-x-3">
                <button className="bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                  View Guide
                </button>
                <button className="bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors">
                  Get Code
                </button>
              </div>
            </div>
          </motion.div>

          {/* Debug: Backend Test */}
        </div>
      </main>

      {/* Embed Code Modal */}
      <EmbedCodeGenerator
        isOpen={showEmbedModal}
        onClose={() => setShowEmbedModal(false)}
        clientData={{
          ...clientData,
          id: clientData.id || user?.id || user?._id,
          productType: clientData.productType || user?.productType || 'watches',
          productName: clientData.productName || user?.productName || '',
          productImageUrl: clientData.productImageUrl || user?.productImageUrl || '',
          caseDimensions: clientData.caseDimensions || user?.caseDimensions || (clientData.productType === 'bracelets' ? '15' : '42'),
          companyName: clientData.companyName || user?.companyName || '',
          email: clientData.email || user?.email || ''
        }}
      />
    </div>
  );
};

export default ClientDashboard; 