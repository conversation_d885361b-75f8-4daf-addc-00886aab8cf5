[{"D:\\Via\\test\\viatryon\\src\\index.js": "1", "D:\\Via\\test\\viatryon\\src\\App.js": "2", "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js": "3", "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx": "4", "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx": "5", "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx": "6", "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx": "7", "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx": "8", "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx": "9", "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx": "10", "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx": "12", "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx": "13", "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx": "14", "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx": "15", "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx": "16", "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx": "17", "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx": "18", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx": "19", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx": "20", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx": "21", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx": "22", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx": "23", "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx": "24", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx": "25", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "26", "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js": "27", "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js": "28", "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js": "29", "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx": "30", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx": "31", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx": "32", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx": "33", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx": "34", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx": "35", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx": "36", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "37", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx": "38", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx": "39", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx": "40", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx": "41", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "42", "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx": "43", "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx": "44", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx": "45", "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx": "46", "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx": "47", "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js": "48", "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js": "49", "D:\\Via\\test\\viatryon\\src\\pages\\Pricing.jsx": "50", "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Requests.jsx": "51", "D:\\Via\\test\\viatryon\\src\\pages\\ProductShowcase.jsx": "52", "D:\\Via\\test\\viatryon\\src\\components\\FAQ.jsx": "53"}, {"size": 653, "mtime": 1749456773932, "results": "54", "hashOfConfig": "55"}, {"size": 5903, "mtime": 1749916387145, "results": "56", "hashOfConfig": "55"}, {"size": 362, "mtime": 1746042995461, "results": "57", "hashOfConfig": "55"}, {"size": 14301, "mtime": 1749831449732, "results": "58", "hashOfConfig": "55"}, {"size": 12354, "mtime": 1749806501555, "results": "59", "hashOfConfig": "55"}, {"size": 5641, "mtime": 1749916315764, "results": "60", "hashOfConfig": "55"}, {"size": 17427, "mtime": 1749770784747, "results": "61", "hashOfConfig": "55"}, {"size": 16159, "mtime": 1749770149661, "results": "62", "hashOfConfig": "55"}, {"size": 40332, "mtime": 1749996668798, "results": "63", "hashOfConfig": "55"}, {"size": 21409, "mtime": 1749770784750, "results": "64", "hashOfConfig": "55"}, {"size": 6108, "mtime": 1746344994148, "results": "65", "hashOfConfig": "55"}, {"size": 91733, "mtime": 1750004651624, "results": "66", "hashOfConfig": "55"}, {"size": 15978, "mtime": 1749770385891, "results": "67", "hashOfConfig": "55"}, {"size": 7923, "mtime": 1749663944103, "results": "68", "hashOfConfig": "55"}, {"size": 117906, "mtime": 1750011355041, "results": "69", "hashOfConfig": "55"}, {"size": 11983, "mtime": 1749831747900, "results": "70", "hashOfConfig": "55"}, {"size": 20122, "mtime": 1749823259206, "results": "71", "hashOfConfig": "55"}, {"size": 15030, "mtime": 1749744585299, "results": "72", "hashOfConfig": "55"}, {"size": 23721, "mtime": 1750011043759, "results": "73", "hashOfConfig": "55"}, {"size": 54295, "mtime": 1749843751984, "results": "74", "hashOfConfig": "55"}, {"size": 667, "mtime": 1749456774086, "results": "75", "hashOfConfig": "55"}, {"size": 12623, "mtime": 1749650927488, "results": "76", "hashOfConfig": "55"}, {"size": 11461, "mtime": 1749741582394, "results": "77", "hashOfConfig": "55"}, {"size": 18892, "mtime": 1750010934157, "results": "78", "hashOfConfig": "55"}, {"size": 5020, "mtime": 1749639557515, "results": "79", "hashOfConfig": "55"}, {"size": 2932, "mtime": 1749671240558, "results": "80", "hashOfConfig": "55"}, {"size": 10601, "mtime": 1748277235110, "results": "81", "hashOfConfig": "55"}, {"size": 4297, "mtime": 1748283089634, "results": "82", "hashOfConfig": "55"}, {"size": 8174, "mtime": 1748283061371, "results": "83", "hashOfConfig": "55"}, {"size": 15401, "mtime": 1750011023288, "results": "84", "hashOfConfig": "55"}, {"size": 20917, "mtime": 1749640743996, "results": "85", "hashOfConfig": "55"}, {"size": 14795, "mtime": 1749645956053, "results": "86", "hashOfConfig": "55"}, {"size": 11229, "mtime": 1749639963466, "results": "87", "hashOfConfig": "55"}, {"size": 15809, "mtime": 1749648036920, "results": "88", "hashOfConfig": "55"}, {"size": 13744, "mtime": 1749640585904, "results": "89", "hashOfConfig": "55"}, {"size": 13635, "mtime": 1749647517295, "results": "90", "hashOfConfig": "55"}, {"size": 9056, "mtime": 1749739554342, "results": "91", "hashOfConfig": "55"}, {"size": 11669, "mtime": 1749670101992, "results": "92", "hashOfConfig": "55"}, {"size": 9424, "mtime": 1749739552961, "results": "93", "hashOfConfig": "55"}, {"size": 8612, "mtime": 1749809830670, "results": "94", "hashOfConfig": "55"}, {"size": 12795, "mtime": 1749670101992, "results": "95", "hashOfConfig": "55"}, {"size": 11305, "mtime": 1749739340824, "results": "96", "hashOfConfig": "55"}, {"size": 12153, "mtime": 1749490264599, "results": "97", "hashOfConfig": "55"}, {"size": 8780, "mtime": 1749656007836, "results": "98", "hashOfConfig": "55"}, {"size": 10137, "mtime": 1749659801764, "results": "99", "hashOfConfig": "55"}, {"size": 8611, "mtime": 1749740990270, "results": "100", "hashOfConfig": "55"}, {"size": 3725, "mtime": 1749659859206, "results": "101", "hashOfConfig": "55"}, {"size": 13421, "mtime": 1749929274266, "results": "102", "hashOfConfig": "55"}, {"size": 2855, "mtime": 1749926544877, "results": "103", "hashOfConfig": "55"}, {"size": 14071, "mtime": 1749828509330, "results": "104", "hashOfConfig": "55"}, {"size": 19788, "mtime": 1749809782787, "results": "105", "hashOfConfig": "55"}, {"size": 19137, "mtime": 1749831261266, "results": "106", "hashOfConfig": "55"}, {"size": 13894, "mtime": 1749917181013, "results": "107", "hashOfConfig": "55"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "snlcfk", {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Via\\test\\viatryon\\src\\index.js", [], [], "D:\\Via\\test\\viatryon\\src\\App.js", ["267"], [], "D:\\Via\\test\\viatryon\\src\\reportWebVitals.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Navbar.jsx", ["268"], [], "D:\\Via\\test\\viatryon\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\components\\Footer.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Bracelets.jsx", ["269", "270", "271", "272", "273", "274"], [], "D:\\Via\\test\\viatryon\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Home.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Watches.jsx", ["275", "276", "277", "278", "279"], [], "D:\\Via\\test\\viatryon\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\VirtualTryOn.jsx", ["280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292"], [], "D:\\Via\\test\\viatryon\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Login.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Tryon.jsx", ["293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308"], [], "D:\\Via\\test\\viatryon\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\AdminDashboard.jsx", ["309", "310", "311"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Clients.jsx", ["312", "313", "314", "315", "316", "317", "318", "319", "320", "321"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Settings.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientSettings.jsx", ["322"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\ClientDashboard.jsx", ["323", "324", "325"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\AdminAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\data\\productCollections.js", [], [], "D:\\Via\\test\\viatryon\\src\\context\\CartContext.js", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\test\\viatryon\\src\\components\\EmbedCodeGenerator.jsx", ["326", "327", "328"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\Overview.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\TryOnAnalytics.jsx", ["329", "330", "331"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ClientPerformance.jsx", ["332", "333", "334", "335", "336", "337", "338"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\GeographicAnalytics.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\ProductAnalytics.jsx", ["339"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\analytics\\BehaviorAnalytics.jsx", ["340", "341", "342"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["343", "344", "345", "346", "347"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\DeviceStats.jsx", ["348", "349", "350", "351", "352", "353", "354", "355"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\Overview.jsx", ["356", "357"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminSidebar.jsx", ["358"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["359", "360"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["361"], [], "D:\\Via\\test\\viatryon\\src\\components\\admin\\AdminNavbar.jsx", ["362"], [], "D:\\Via\\test\\viatryon\\src\\pages\\client\\analytics\\GeographicData.jsx", ["363"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientNavbar.jsx", ["364"], [], "D:\\Via\\test\\viatryon\\src\\components\\client\\ClientSidebar.jsx", ["365"], [], "D:\\Via\\test\\viatryon\\src\\components\\debug\\BackendTest.jsx", [], [], "D:\\Via\\test\\viatryon\\src\\utils\\backgroundRemover.js", ["366"], [], "D:\\Via\\test\\viatryon\\src\\utils\\uShapeCutter.js", [], [], "D:\\Via\\test\\viatryon\\src\\pages\\Pricing.jsx", ["367"], [], "D:\\Via\\test\\viatryon\\src\\pages\\admin\\Requests.jsx", ["368", "369"], [], "D:\\Via\\test\\viatryon\\src\\pages\\ProductShowcase.jsx", ["370", "371", "372"], [], "D:\\Via\\test\\viatryon\\src\\components\\FAQ.jsx", [], [], {"ruleId": "373", "severity": 1, "message": "374", "line": 5, "column": 8, "nodeType": "375", "messageId": "376", "endLine": 5, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "377", "line": 37, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 37, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "378", "line": 2, "column": 18, "nodeType": "375", "messageId": "376", "endLine": 2, "endColumn": 33}, {"ruleId": "373", "severity": 1, "message": "379", "line": 10, "column": 24, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 39}, {"ruleId": "373", "severity": 1, "message": "380", "line": 12, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "381", "line": 13, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 13, "endColumn": 22}, {"ruleId": "373", "severity": 1, "message": "382", "line": 16, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "383", "line": 50, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 50, "endColumn": 26}, {"ruleId": "373", "severity": 1, "message": "378", "line": 2, "column": 18, "nodeType": "375", "messageId": "376", "endLine": 2, "endColumn": 33}, {"ruleId": "373", "severity": 1, "message": "379", "line": 10, "column": 24, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 39}, {"ruleId": "373", "severity": 1, "message": "380", "line": 12, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "384", "line": 13, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 13, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "382", "line": 16, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "385", "line": 221, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 221, "endColumn": 30}, {"ruleId": "373", "severity": 1, "message": "386", "line": 391, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 391, "endColumn": 27}, {"ruleId": "373", "severity": 1, "message": "387", "line": 397, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 397, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "388", "line": 857, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 857, "endColumn": 19}, {"ruleId": "389", "severity": 1, "message": "390", "line": 1100, "column": 6, "nodeType": "391", "endLine": 1100, "endColumn": 55, "suggestions": "392"}, {"ruleId": "373", "severity": 1, "message": "393", "line": 1116, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1116, "endColumn": 28}, {"ruleId": "373", "severity": 1, "message": "394", "line": 1474, "column": 25, "nodeType": "375", "messageId": "376", "endLine": 1474, "endColumn": 41}, {"ruleId": "395", "severity": 1, "message": "396", "line": 2788, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2788, "endColumn": 29}, {"ruleId": "395", "severity": 1, "message": "399", "line": 2800, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2800, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "400", "line": 2807, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2807, "endColumn": 22}, {"ruleId": "395", "severity": 1, "message": "401", "line": 2813, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2813, "endColumn": 16}, {"ruleId": "395", "severity": 1, "message": "402", "line": 2821, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2821, "endColumn": 15}, {"ruleId": "395", "severity": 1, "message": "403", "line": 2830, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 2830, "endColumn": 15}, {"ruleId": "373", "severity": 1, "message": "404", "line": 188, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 188, "endColumn": 43}, {"ruleId": "373", "severity": 1, "message": "405", "line": 462, "column": 17, "nodeType": "375", "messageId": "376", "endLine": 462, "endColumn": 25}, {"ruleId": "389", "severity": 1, "message": "406", "line": 539, "column": 6, "nodeType": "391", "endLine": 539, "endColumn": 17, "suggestions": "407"}, {"ruleId": "373", "severity": 1, "message": "408", "line": 592, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 592, "endColumn": 23}, {"ruleId": "373", "severity": 1, "message": "409", "line": 593, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 593, "endColumn": 23}, {"ruleId": "373", "severity": 1, "message": "410", "line": 594, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 594, "endColumn": 26}, {"ruleId": "373", "severity": 1, "message": "387", "line": 665, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 665, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "411", "line": 1280, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1280, "endColumn": 23}, {"ruleId": "373", "severity": 1, "message": "412", "line": 1326, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1326, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "413", "line": 1335, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1335, "endColumn": 20}, {"ruleId": "373", "severity": 1, "message": "388", "line": 1591, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1591, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "393", "line": 1643, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 1643, "endColumn": 28}, {"ruleId": "389", "severity": 1, "message": "390", "line": 1732, "column": 6, "nodeType": "391", "endLine": 1732, "endColumn": 55, "suggestions": "414"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 1796, "column": 6, "nodeType": "391", "endLine": 1796, "endColumn": 55, "suggestions": "415"}, {"ruleId": "373", "severity": 1, "message": "394", "line": 2153, "column": 25, "nodeType": "375", "messageId": "376", "endLine": 2153, "endColumn": 41}, {"ruleId": "395", "severity": 1, "message": "416", "line": 3225, "column": 5, "nodeType": "397", "messageId": "398", "endLine": 3225, "endColumn": 11}, {"ruleId": "373", "severity": 1, "message": "417", "line": 5, "column": 27, "nodeType": "375", "messageId": "376", "endLine": 5, "endColumn": 35}, {"ruleId": "373", "severity": 1, "message": "418", "line": 5, "column": 37, "nodeType": "375", "messageId": "376", "endLine": 5, "endColumn": 40}, {"ruleId": "373", "severity": 1, "message": "419", "line": 19, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 19, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "420", "line": 6, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 6, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "421", "line": 6, "column": 36, "nodeType": "375", "messageId": "376", "endLine": 6, "endColumn": 41}, {"ruleId": "373", "severity": 1, "message": "422", "line": 7, "column": 19, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 24}, {"ruleId": "373", "severity": 1, "message": "423", "line": 7, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 36}, {"ruleId": "373", "severity": 1, "message": "424", "line": 8, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 11}, {"ruleId": "373", "severity": 1, "message": "425", "line": 8, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "426", "line": 8, "column": 21, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 24}, {"ruleId": "373", "severity": 1, "message": "427", "line": 8, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "428", "line": 8, "column": 34, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 46}, {"ruleId": "389", "severity": 1, "message": "429", "line": 76, "column": 6, "nodeType": "391", "endLine": 76, "endColumn": 35, "suggestions": "430"}, {"ruleId": "373", "severity": 1, "message": "431", "line": 4, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 4, "endColumn": 16}, {"ruleId": "373", "severity": 1, "message": "432", "line": 7, "column": 15, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "421", "line": 7, "column": 47, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 52}, {"ruleId": "373", "severity": 1, "message": "423", "line": 7, "column": 54, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 64}, {"ruleId": "373", "severity": 1, "message": "433", "line": 3, "column": 23, "nodeType": "375", "messageId": "376", "endLine": 3, "endColumn": 27}, {"ruleId": "434", "severity": 1, "message": "435", "line": 304, "column": 31, "nodeType": "436", "endLine": 304, "endColumn": 65}, {"ruleId": "434", "severity": 1, "message": "435", "line": 305, "column": 21, "nodeType": "436", "endLine": 305, "endColumn": 60}, {"ruleId": "373", "severity": 1, "message": "437", "line": 14, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "432", "line": 19, "column": 22, "nodeType": "375", "messageId": "376", "endLine": 19, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "438", "line": 19, "column": 46, "nodeType": "375", "messageId": "376", "endLine": 19, "endColumn": 53}, {"ruleId": "373", "severity": 1, "message": "439", "line": 11, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 11, "endColumn": 11}, {"ruleId": "373", "severity": 1, "message": "440", "line": 12, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 6}, {"ruleId": "373", "severity": 1, "message": "441", "line": 13, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 13, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "442", "line": 14, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "443", "line": 15, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "437", "line": 16, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "432", "line": 19, "column": 22, "nodeType": "375", "messageId": "376", "endLine": 19, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "432", "line": 16, "column": 22, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "444", "line": 4, "column": 17, "nodeType": "375", "messageId": "376", "endLine": 4, "endColumn": 23}, {"ruleId": "373", "severity": 1, "message": "445", "line": 4, "column": 25, "nodeType": "375", "messageId": "376", "endLine": 4, "endColumn": 29}, {"ruleId": "373", "severity": 1, "message": "446", "line": 4, "column": 31, "nodeType": "375", "messageId": "376", "endLine": 4, "endColumn": 37}, {"ruleId": "373", "severity": 1, "message": "442", "line": 6, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 6, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "443", "line": 7, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "437", "line": 12, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "432", "line": 15, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 36}, {"ruleId": "373", "severity": 1, "message": "447", "line": 94, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 94, "endColumn": 26}, {"ruleId": "373", "severity": 1, "message": "417", "line": 7, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 11}, {"ruleId": "373", "severity": 1, "message": "418", "line": 8, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 6}, {"ruleId": "373", "severity": 1, "message": "448", "line": 9, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 9, "endColumn": 8}, {"ruleId": "373", "severity": 1, "message": "449", "line": 10, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 8}, {"ruleId": "373", "severity": 1, "message": "450", "line": 11, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 11, "endColumn": 16}, {"ruleId": "373", "severity": 1, "message": "442", "line": 15, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "443", "line": 16, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "451", "line": 121, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 121, "endColumn": 21}, {"ruleId": "373", "severity": 1, "message": "452", "line": 14, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 13}, {"ruleId": "373", "severity": 1, "message": "432", "line": 14, "column": 22, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "380", "line": 11, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 11, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "442", "line": 4, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 4, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "443", "line": 5, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 5, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "437", "line": 12, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "380", "line": 14, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "437", "line": 9, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 9, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "380", "line": 10, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "453", "line": 1, "column": 17, "nodeType": "375", "messageId": "376", "endLine": 1, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "454", "line": 160, "column": 7, "nodeType": "375", "messageId": "376", "endLine": 160, "endColumn": 18}, {"ruleId": "373", "severity": 1, "message": "455", "line": 38, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 38, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "456", "line": 5, "column": 64, "nodeType": "375", "messageId": "376", "endLine": 5, "endColumn": 70}, {"ruleId": "389", "severity": 1, "message": "457", "line": 31, "column": 6, "nodeType": "391", "endLine": 31, "endColumn": 26, "suggestions": "458"}, {"ruleId": "373", "severity": 1, "message": "459", "line": 13, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 13, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "460", "line": 14, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "461", "line": 16, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 19}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'isClient' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", "'setActiveFilter' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'heroBracelet' is assigned a value but never used.", "'filterCategories' is assigned a value but never used.", "'filteredBracelets' is assigned a value but never used.", "'heroWatch' is assigned a value but never used.", "'measureWatchFromImage' is assigned a value but never used.", "'handleGenderChange' is assigned a value but never used.", "'getWatchPosition' is assigned a value but never used.", "'handleBack' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleCapture'. Either include it or remove the dependency array.", "ArrayExpression", ["462"], "'handleBackWithReset' is assigned a value but never used.", "'defaultWristSize' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'brandingContainerTangiblee'.", "ObjectExpression", "unexpected", "Duplicate key 'brandingLogoTangiblee'.", "Duplicate key 'brandingTextStacked'.", "Duplicate key 'poweredByText'.", "Duplicate key 'viatryonText'.", "Duplicate key 'backArrowBtn'.", "'setQualityMetrics' is assigned a value but never used.", "'imageUrl' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'endAnalyticsSession', 'initializeEnhancedTracking', and 'startAnalyticsSession'. Either include them or remove the dependency array.", ["463"], "'MIN_WRIST_SIZE' is assigned a value but never used.", "'MAX_WRIST_SIZE' is assigned a value but never used.", "'ASSUMED_DIAL_SIZE' is assigned a value but never used.", "'takeScreenshot' is assigned a value but never used.", "'handleZoom' is assigned a value but never used.", "'handleShare' is assigned a value but never used.", ["464"], ["465"], "Duplicate key 'border'.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'businessMetrics' is assigned a value but never used.", "'Search' is defined but never used.", "'Globe' is defined but never used.", "'Clock' is defined but never used.", "'Smartphone' is defined but never used.", "'Calendar' is defined but never used.", "'Target' is defined but never used.", "'Zap' is defined but never used.", "'MapPin' is defined but never used.", "'ChevronRight' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchClients'. Either include it or remove the dependency array.", ["466"], "'motion' is defined but never used.", "'TrendingUp' is defined but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Legend' is defined but never used.", "'Monitor' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Camera' is defined but never used.", "'Hand' is defined but never used.", "'Layers' is defined but never used.", "'totalInteractions' is assigned a value but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'deviceTrends' is assigned a value but never used.", "'Eye' is defined but never used.", "'useState' is defined but never used.", "'isEdgePixel' is assigned a value but never used.", "'logos' is assigned a value but never used.", "'Filter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRequests'. Either include it or remove the dependency array.", ["467"], "'navigate' is assigned a value but never used.", "'location' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "468", "fix": "469"}, {"desc": "470", "fix": "471"}, {"desc": "468", "fix": "472"}, {"desc": "468", "fix": "473"}, {"desc": "474", "fix": "475"}, {"desc": "476", "fix": "477"}, "Update the dependencies array to be: [isCountdownActive, isCaptured, isHandInPosition, handleCapture]", {"range": "478", "text": "479"}, "Update the dependencies array to be: [endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", {"range": "480", "text": "481"}, {"range": "482", "text": "479"}, {"range": "483", "text": "479"}, "Update the dependencies array to be: [fetchClients, searchQuery, selectedStatus]", {"range": "484", "text": "485"}, "Update the dependencies array to be: [activeTab, fetchRequests, filters]", {"range": "486", "text": "487"}, [37257, 37306], "[isCountdownActive, isCaptured, isHandInPosition, handleCapture]", [16669, 16680], "[endAnalyticsSession, initializeEnhancedTracking, startAnalyticsSession, urlParams]", [56967, 57016], [58907, 58956], [2748, 2777], "[fetchClients, searchQuery, selectedStatus]", [1143, 1163], "[activeTab, fetchRequests, filters]"]