{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\components\\\\EmbedCodeGenerator.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Copy, Check, Code, Globe, Smartphone, Monitor } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmbedCodeGenerator = ({\n  isOpen,\n  onClose,\n  clientData\n}) => {\n  _s();\n  const [copied, setCopied] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState('');\n  const [buttonStyle, setButtonStyle] = useState('default');\n  const [buttonSize, setButtonSize] = useState('medium');\n  const [caseDimensions, setCaseDimensions] = useState((clientData === null || clientData === void 0 ? void 0 : clientData.productType) === 'bracelets' ? '15' : '42');\n  const [productType, setProductType] = useState((clientData === null || clientData === void 0 ? void 0 : clientData.productType) || 'watches');\n\n  // Update productType and dimensions when clientData changes\n  useEffect(() => {\n    if (clientData !== null && clientData !== void 0 && clientData.productType) {\n      setProductType(clientData.productType);\n      setCaseDimensions(clientData.productType === 'bracelets' ? '15' : '42');\n    }\n  }, [clientData === null || clientData === void 0 ? void 0 : clientData.productType]);\n\n  // Update dimensions when product type changes\n  useEffect(() => {\n    setCaseDimensions(productType === 'bracelets' ? '15' : '42');\n  }, [productType]);\n  const generateEmbedCode = () => {\n    // Automatically use the logged-in client's ID from the database (MongoDB ObjectId without 'object' prefix)\n    const clientId = (clientData === null || clientData === void 0 ? void 0 : clientData.id) || (clientData === null || clientData === void 0 ? void 0 : clientData._id) || 'AUTO_CLIENT_ID';\n    const productImageUrl = selectedProduct || (clientData === null || clientData === void 0 ? void 0 : clientData.productImageUrl) || 'YOUR_PRODUCT_IMAGE_URL';\n    const caseSize = caseDimensions || (clientData === null || clientData === void 0 ? void 0 : clientData.caseDimensions) || '42'; // Use state value\n    const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n    const productName = (clientData === null || clientData === void 0 ? void 0 : clientData.productName) || 'Product Name';\n    return `<!-- ViaTryon Virtual Try-On Button for ${productName} -->\n<!-- Client ID: ${clientId} | Product Type: ${productType} | Size: ${caseSize}mm -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${clientId}', '${caseSize}', '${productType}', '${productName}')\"\n  class=\"viatryon-btn viatryon-btn-${buttonStyle} viatryon-btn-${buttonSize}\"\n  style=\"\n    background-color: ${buttonStyle === 'primary' ? '#2D8C88' : buttonStyle === 'outline' ? 'transparent' : '#333'};\n    color: ${buttonStyle === 'outline' ? '#2D8C88' : 'white'};\n    border: ${buttonStyle === 'outline' ? '2px solid #2D8C88' : 'none'};\n    padding: ${buttonSize === 'small' ? '8px 16px' : buttonSize === 'large' ? '16px 32px' : '12px 24px'};\n    font-size: ${buttonSize === 'small' ? '14px' : buttonSize === 'large' ? '18px' : '16px'};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.opacity='0.8'\"\n  onmouseout=\"this.style.opacity='1'\"\n>\n  Try On Virtually\n</button>\n\n<script>\nfunction openViaTryon(imageUrl, clientId, caseDimensions, productType) {\n  // Construct the ViaTryon URL with parameters\n  const tryonUrl = '${websiteUrl}/tryon?' +\n    'image=' + encodeURIComponent(imageUrl) +\n    '&client=' + encodeURIComponent(clientId) +\n    '&size=' + encodeURIComponent(caseDimensions) +\n    '&type=' + encodeURIComponent(productType || 'watches');\n\n  // Open in new window/tab\n  window.open(tryonUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Usage Instructions:\n1. Replace 'YOUR_PRODUCT_IMAGE_URL' with the actual URL of your product image\n2. Update caseDimensions with the actual case size (e.g., '42' for 42mm)\n3. Make sure the product image has a white background for best results\n-->`;\n  };\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(generateEmbedCode());\n    setCopied(true);\n    setTimeout(() => setCopied(false), 2000);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Virtual Try-On Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: [\"Generate embed code for \", (clientData === null || clientData === void 0 ? void 0 : clientData.companyName) || 'your website']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), (clientData === null || clientData === void 0 ? void 0 : clientData.id) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mt-1\",\n              children: [\"Client ID: \", clientData.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: \"Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Product Image URL\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: selectedProduct,\n                    onChange: e => setSelectedProduct(e.target.value),\n                    placeholder: \"https://yoursite.com/images/watch.png\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"URL of the product image with transparent background\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Product Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: productType,\n                    onChange: e => setProductType(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"watches\",\n                      children: \"Watches\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"bracelets\",\n                      children: \"Bracelets\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Select the type of product for optimal try-on experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: productType === 'watches' ? 'Case Dimensions (mm)' : 'Width (mm)'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: caseDimensions,\n                    onChange: e => setCaseDimensions(e.target.value),\n                    placeholder: productType === 'watches' ? '42' : '15',\n                    min: productType === 'watches' ? '20' : '10',\n                    max: productType === 'watches' ? '60' : '30',\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: productType === 'watches' ? 'Watch case diameter for proper scaling (20-60mm)' : 'Bracelet width for proper scaling (10-30mm)'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Button Style\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: buttonStyle,\n                    onChange: e => setButtonStyle(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"default\",\n                      children: \"Default\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"primary\",\n                      children: \"Primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"outline\",\n                      children: \"Outline\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"minimal\",\n                      children: \"Minimal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Button Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: buttonSize,\n                    onChange: e => setButtonSize(e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"small\",\n                      children: \"Small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"medium\",\n                      children: \"Medium\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"large\",\n                      children: \"Large\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: \"Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${buttonStyle === 'primary' ? 'bg-[#2D8C88] text-white hover:bg-[#236b68]' : buttonStyle === 'outline' ? 'border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white' : buttonStyle === 'minimal' ? 'text-[#2D8C88] hover:bg-[#2D8C88]/10' : 'bg-gray-800 text-white hover:bg-gray-700'} ${buttonSize === 'small' ? 'text-sm px-3 py-1.5' : buttonSize === 'large' ? 'text-lg px-6 py-3' : 'text-base px-4 py-2'}`,\n                    children: [/*#__PURE__*/_jsxDEV(Globe, {\n                      className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 23\n                    }, this), \"Try On Virtually\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4\",\n                children: \"Device Compatibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center p-3 bg-green-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(Smartphone, {\n                    className: \"h-6 w-6 text-green-600 mx-auto mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-green-800\",\n                    children: \"Mobile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-green-600\",\n                    children: \"Optimized\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center p-3 bg-green-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(Monitor, {\n                    className: \"h-6 w-6 text-green-600 mx-auto mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-green-800\",\n                    children: \"Desktop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-green-600\",\n                    children: \"Supported\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center p-3 bg-green-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(Globe, {\n                    className: \"h-6 w-6 text-green-600 mx-auto mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-green-800\",\n                    children: \"Tablet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-green-600\",\n                    children: \"Supported\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"Embed Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: copyToClipboard,\n                className: \"inline-flex items-center px-3 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\",\n                children: [copied ? /*#__PURE__*/_jsxDEV(Check, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 29\n                }, this) : /*#__PURE__*/_jsxDEV(Copy, {\n                  className: \"h-4 w-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 66\n                }, this), copied ? 'Copied!' : 'Copy Code']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-900 rounded-lg p-4 overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                className: \"text-green-400 text-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: generateEmbedCode()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-blue-900 mb-2\",\n                  children: \"Integration Steps:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                  className: \"text-sm text-blue-800 space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"1. Copy the embed code above\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URL\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"3. Update the dimensions (size) for each product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"4. Your client ID is automatically included for analytics tracking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"5. Ensure the product type matches your product category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"6. Paste the code into your product page HTML\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"7. Test the integration on your website\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 p-3 bg-blue-100 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-blue-700\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Note:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 23\n                    }, this), \" Your client ID (\", (clientData === null || clientData === void 0 ? void 0 : clientData.id) || 'YOUR_CLIENT_ID', \") is automatically included in the code for analytics tracking.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-yellow-900 mb-2\",\n                  children: \"Need Help?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-yellow-800\",\n                  children: [\"Check our \", /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"underline\",\n                    children: \"integration guide\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 31\n                  }, this), \" or\", /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"underline ml-1\",\n                    children: \"contact support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 21\n                  }, this), \" for assistance.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(EmbedCodeGenerator, \"Ds7blm3OC9c4GztmIVGkw/danfI=\");\n_c = EmbedCodeGenerator;\nexport default EmbedCodeGenerator;\nvar _c;\n$RefreshReg$(_c, \"EmbedCodeGenerator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Copy", "Check", "Code", "Globe", "Smartphone", "Monitor", "jsxDEV", "_jsxDEV", "EmbedCodeGenerator", "isOpen", "onClose", "clientData", "_s", "copied", "setCopied", "selectedProduct", "setSelectedProduct", "buttonStyle", "setButtonStyle", "buttonSize", "setButtonSize", "caseDimensions", "setCaseDimensions", "productType", "setProductType", "generateEmbedCode", "clientId", "id", "_id", "productImageUrl", "caseSize", "websiteUrl", "process", "env", "REACT_APP_WEBSITE_URL", "productName", "copyToClipboard", "navigator", "clipboard", "writeText", "setTimeout", "className", "children", "div", "initial", "opacity", "scale", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "companyName", "onClick", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "type", "value", "onChange", "e", "target", "placeholder", "min", "max", "href", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/components/EmbedCodeGenerator.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Copy, Check, Code, Globe, Smartphone, Monitor } from 'lucide-react';\n\nconst EmbedCodeGenerator = ({ isOpen, onClose, clientData }) => {\n  const [copied, setCopied] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState('');\n  const [buttonStyle, setButtonStyle] = useState('default');\n  const [buttonSize, setButtonSize] = useState('medium');\n  const [caseDimensions, setCaseDimensions] = useState(\n    clientData?.productType === 'bracelets' ? '15' : '42'\n  );\n  const [productType, setProductType] = useState(clientData?.productType || 'watches');\n\n  // Update productType and dimensions when clientData changes\n  useEffect(() => {\n    if (clientData?.productType) {\n      setProductType(clientData.productType);\n      setCaseDimensions(clientData.productType === 'bracelets' ? '15' : '42');\n    }\n  }, [clientData?.productType]);\n\n  // Update dimensions when product type changes\n  useEffect(() => {\n    setCaseDimensions(productType === 'bracelets' ? '15' : '42');\n  }, [productType]);\n\n  const generateEmbedCode = () => {\n    // Automatically use the logged-in client's ID from the database (MongoDB ObjectId without 'object' prefix)\n    const clientId = clientData?.id || clientData?._id || 'AUTO_CLIENT_ID';\n    const productImageUrl = selectedProduct || clientData?.productImageUrl || 'YOUR_PRODUCT_IMAGE_URL';\n    const caseSize = caseDimensions || clientData?.caseDimensions || '42'; // Use state value\n    const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n    const productName = clientData?.productName || 'Product Name';\n\n    return `<!-- ViaTryon Virtual Try-On Button for ${productName} -->\n<!-- Client ID: ${clientId} | Product Type: ${productType} | Size: ${caseSize}mm -->\n<button\n  onclick=\"openViaTryon('${productImageUrl}', '${clientId}', '${caseSize}', '${productType}', '${productName}')\"\n  class=\"viatryon-btn viatryon-btn-${buttonStyle} viatryon-btn-${buttonSize}\"\n  style=\"\n    background-color: ${buttonStyle === 'primary' ? '#2D8C88' : buttonStyle === 'outline' ? 'transparent' : '#333'};\n    color: ${buttonStyle === 'outline' ? '#2D8C88' : 'white'};\n    border: ${buttonStyle === 'outline' ? '2px solid #2D8C88' : 'none'};\n    padding: ${buttonSize === 'small' ? '8px 16px' : buttonSize === 'large' ? '16px 32px' : '12px 24px'};\n    font-size: ${buttonSize === 'small' ? '14px' : buttonSize === 'large' ? '18px' : '16px'};\n    border-radius: 8px;\n    cursor: pointer;\n    font-weight: 600;\n    transition: all 0.3s ease;\n  \"\n  onmouseover=\"this.style.opacity='0.8'\"\n  onmouseout=\"this.style.opacity='1'\"\n>\n  Try On Virtually\n</button>\n\n<script>\nfunction openViaTryon(imageUrl, clientId, caseDimensions, productType) {\n  // Construct the ViaTryon URL with parameters\n  const tryonUrl = '${websiteUrl}/tryon?' +\n    'image=' + encodeURIComponent(imageUrl) +\n    '&client=' + encodeURIComponent(clientId) +\n    '&size=' + encodeURIComponent(caseDimensions) +\n    '&type=' + encodeURIComponent(productType || 'watches');\n\n  // Open in new window/tab\n  window.open(tryonUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');\n}\n</script>\n\n<!-- Usage Instructions:\n1. Replace 'YOUR_PRODUCT_IMAGE_URL' with the actual URL of your product image\n2. Update caseDimensions with the actual case size (e.g., '42' for 42mm)\n3. Make sure the product image has a white background for best results\n-->`;\n  };\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(generateEmbedCode());\n    setCopied(true);\n    setTimeout(() => setCopied(false), 2000);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.95 }}\n        animate={{ opacity: 1, scale: 1 }}\n        className=\"bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\"\n      >\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-xl font-semibold text-gray-900\">Virtual Try-On Integration</h2>\n              <p className=\"text-gray-600\">Generate embed code for {clientData?.companyName || 'your website'}</p>\n              {clientData?.id && (\n                <p className=\"text-sm text-gray-500 mt-1\">Client ID: {clientData.id}</p>\n              )}\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Configuration Panel */}\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Configuration</h3>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Product Image URL\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={selectedProduct}\n                      onChange={(e) => setSelectedProduct(e.target.value)}\n                      placeholder=\"https://yoursite.com/images/watch.png\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      URL of the product image with transparent background\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Product Type\n                    </label>\n                    <select\n                      value={productType}\n                      onChange={(e) => setProductType(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    >\n                      <option value=\"watches\">Watches</option>\n                      <option value=\"bracelets\">Bracelets</option>\n                    </select>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Select the type of product for optimal try-on experience\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {productType === 'watches' ? 'Case Dimensions (mm)' : 'Width (mm)'}\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={caseDimensions}\n                      onChange={(e) => setCaseDimensions(e.target.value)}\n                      placeholder={productType === 'watches' ? '42' : '15'}\n                      min={productType === 'watches' ? '20' : '10'}\n                      max={productType === 'watches' ? '60' : '30'}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {productType === 'watches'\n                        ? 'Watch case diameter for proper scaling (20-60mm)'\n                        : 'Bracelet width for proper scaling (10-30mm)'\n                      }\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Button Style\n                    </label>\n                    <select\n                      value={buttonStyle}\n                      onChange={(e) => setButtonStyle(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    >\n                      <option value=\"default\">Default</option>\n                      <option value=\"primary\">Primary</option>\n                      <option value=\"outline\">Outline</option>\n                      <option value=\"minimal\">Minimal</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Button Size\n                    </label>\n                    <select\n                      value={buttonSize}\n                      onChange={(e) => setButtonSize(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                    >\n                      <option value=\"small\">Small</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"large\">Large</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* Preview */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Preview</h3>\n                <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n                  <div className=\"flex items-center justify-center\">\n                    <button\n                      className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${\n                        buttonStyle === 'primary' ? 'bg-[#2D8C88] text-white hover:bg-[#236b68]' :\n                        buttonStyle === 'outline' ? 'border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white' :\n                        buttonStyle === 'minimal' ? 'text-[#2D8C88] hover:bg-[#2D8C88]/10' :\n                        'bg-gray-800 text-white hover:bg-gray-700'\n                      } ${\n                        buttonSize === 'small' ? 'text-sm px-3 py-1.5' :\n                        buttonSize === 'large' ? 'text-lg px-6 py-3' :\n                        'text-base px-4 py-2'\n                      }`}\n                    >\n                      <Globe className=\"h-4 w-4 mr-2\" />\n                      Try On Virtually\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Device Compatibility */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Compatibility</h3>\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                    <Smartphone className=\"h-6 w-6 text-green-600 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium text-green-800\">Mobile</p>\n                    <p className=\"text-xs text-green-600\">Optimized</p>\n                  </div>\n                  <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                    <Monitor className=\"h-6 w-6 text-green-600 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium text-green-800\">Desktop</p>\n                    <p className=\"text-xs text-green-600\">Supported</p>\n                  </div>\n                  <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                    <Globe className=\"h-6 w-6 text-green-600 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium text-green-800\">Tablet</p>\n                    <p className=\"text-xs text-green-600\">Supported</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Code Panel */}\n            <div>\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Embed Code</h3>\n                <button\n                  onClick={copyToClipboard}\n                  className=\"inline-flex items-center px-3 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors\"\n                >\n                  {copied ? <Check className=\"h-4 w-4 mr-2\" /> : <Copy className=\"h-4 w-4 mr-2\" />}\n                  {copied ? 'Copied!' : 'Copy Code'}\n                </button>\n              </div>\n              \n              <div className=\"bg-gray-900 rounded-lg p-4 overflow-x-auto\">\n                <pre className=\"text-green-400 text-sm\">\n                  <code>{generateEmbedCode()}</code>\n                </pre>\n              </div>\n\n              <div className=\"mt-6 space-y-4\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Integration Steps:</h4>\n                  <ol className=\"text-sm text-blue-800 space-y-1\">\n                    <li>1. Copy the embed code above</li>\n                    <li>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URL</li>\n                    <li>3. Update the dimensions (size) for each product</li>\n                    <li>4. Your client ID is automatically included for analytics tracking</li>\n                    <li>5. Ensure the product type matches your product category</li>\n                    <li>6. Paste the code into your product page HTML</li>\n                    <li>7. Test the integration on your website</li>\n                  </ol>\n                  <div className=\"mt-3 p-3 bg-blue-100 rounded-lg\">\n                    <p className=\"text-xs text-blue-700\">\n                      <strong>Note:</strong> Your client ID ({clientData?.id || 'YOUR_CLIENT_ID'}) is automatically included in the code for analytics tracking.\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-yellow-900 mb-2\">Need Help?</h4>\n                  <p className=\"text-sm text-yellow-800\">\n                    Check our <a href=\"#\" className=\"underline\">integration guide</a> or \n                    <a href=\"#\" className=\"underline ml-1\">contact support</a> for assistance.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default EmbedCodeGenerator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,QAAQ,CAAC;EACtD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAClD,CAAAc,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEY,WAAW,MAAK,WAAW,GAAG,IAAI,GAAG,IACnD,CAAC;EACD,MAAM,CAACA,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAAc,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEY,WAAW,KAAI,SAAS,CAAC;;EAEpF;EACAzB,SAAS,CAAC,MAAM;IACd,IAAIa,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEY,WAAW,EAAE;MAC3BC,cAAc,CAACb,UAAU,CAACY,WAAW,CAAC;MACtCD,iBAAiB,CAACX,UAAU,CAACY,WAAW,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;IACzE;EACF,CAAC,EAAE,CAACZ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEY,WAAW,CAAC,CAAC;;EAE7B;EACAzB,SAAS,CAAC,MAAM;IACdwB,iBAAiB,CAACC,WAAW,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;EAC9D,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMC,QAAQ,GAAG,CAAAf,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgB,EAAE,MAAIhB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,GAAG,KAAI,gBAAgB;IACtE,MAAMC,eAAe,GAAGd,eAAe,KAAIJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,eAAe,KAAI,wBAAwB;IAClG,MAAMC,QAAQ,GAAGT,cAAc,KAAIV,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEU,cAAc,KAAI,IAAI,CAAC,CAAC;IACvE,MAAMU,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,sBAAsB;IAC9E,MAAMC,WAAW,GAAG,CAAAxB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,WAAW,KAAI,cAAc;IAE7D,OAAO,2CAA2CA,WAAW;AACjE,kBAAkBT,QAAQ,oBAAoBH,WAAW,YAAYO,QAAQ;AAC7E;AACA,2BAA2BD,eAAe,OAAOH,QAAQ,OAAOI,QAAQ,OAAOP,WAAW,OAAOY,WAAW;AAC5G,qCAAqClB,WAAW,iBAAiBE,UAAU;AAC3E;AACA,wBAAwBF,WAAW,KAAK,SAAS,GAAG,SAAS,GAAGA,WAAW,KAAK,SAAS,GAAG,aAAa,GAAG,MAAM;AAClH,aAAaA,WAAW,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;AAC5D,cAAcA,WAAW,KAAK,SAAS,GAAG,mBAAmB,GAAG,MAAM;AACtE,eAAeE,UAAU,KAAK,OAAO,GAAG,UAAU,GAAGA,UAAU,KAAK,OAAO,GAAG,WAAW,GAAG,WAAW;AACvG,iBAAiBA,UAAU,KAAK,OAAO,GAAG,MAAM,GAAGA,UAAU,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBY,UAAU;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACd,iBAAiB,CAAC,CAAC,CAAC;IAClDX,SAAS,CAAC,IAAI,CAAC;IACf0B,UAAU,CAAC,MAAM1B,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC1C,CAAC;EAED,IAAI,CAACL,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKkC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFnC,OAAA,CAACR,MAAM,CAAC4C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAE;MACrCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;MAClCL,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAEvFnC,OAAA;QAAKkC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CnC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAIkC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAA0B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnF5C,OAAA;cAAGkC,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,0BAAwB,EAAC,CAAA/B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyC,WAAW,KAAI,cAAc;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACnG,CAAAxC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgB,EAAE,kBACbpB,OAAA;cAAGkC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,aAAW,EAAC/B,UAAU,CAACgB,EAAE;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACxE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN5C,OAAA;YACE8C,OAAO,EAAE3C,OAAQ;YACjB+B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CnC,OAAA;cAAKkC,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAd,QAAA,eAC5EnC,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5C,OAAA;QAAKkC,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBnC,OAAA;UAAKkC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpDnC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAIkC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEzE5C,OAAA;gBAAKkC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnC,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAOkC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR5C,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAE/C,eAAgB;oBACvBgD,QAAQ,EAAGC,CAAC,IAAKhD,kBAAkB,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACpDI,WAAW,EAAC,uCAAuC;oBACnDzB,SAAS,EAAC;kBAAkI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC,eACF5C,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN5C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAOkC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR5C,OAAA;oBACEuD,KAAK,EAAEvC,WAAY;oBACnBwC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAChDrB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5InC,OAAA;sBAAQuD,KAAK,EAAC,SAAS;sBAAApB,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC5C,OAAA;sBAAQuD,KAAK,EAAC,WAAW;sBAAApB,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACT5C,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN5C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAOkC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAC5DnB,WAAW,KAAK,SAAS,GAAG,sBAAsB,GAAG;kBAAY;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACR5C,OAAA;oBACEsD,IAAI,EAAC,QAAQ;oBACbC,KAAK,EAAEzC,cAAe;oBACtB0C,QAAQ,EAAGC,CAAC,IAAK1C,iBAAiB,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACnDI,WAAW,EAAE3C,WAAW,KAAK,SAAS,GAAG,IAAI,GAAG,IAAK;oBACrD4C,GAAG,EAAE5C,WAAW,KAAK,SAAS,GAAG,IAAI,GAAG,IAAK;oBAC7C6C,GAAG,EAAE7C,WAAW,KAAK,SAAS,GAAG,IAAI,GAAG,IAAK;oBAC7CkB,SAAS,EAAC;kBAAkI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7I,CAAC,eACF5C,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtCnB,WAAW,KAAK,SAAS,GACtB,kDAAkD,GAClD;kBAA6C;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN5C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAOkC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR5C,OAAA;oBACEuD,KAAK,EAAE7C,WAAY;oBACnB8C,QAAQ,EAAGC,CAAC,IAAK9C,cAAc,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAChDrB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5InC,OAAA;sBAAQuD,KAAK,EAAC,SAAS;sBAAApB,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC5C,OAAA;sBAAQuD,KAAK,EAAC,SAAS;sBAAApB,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC5C,OAAA;sBAAQuD,KAAK,EAAC,SAAS;sBAAApB,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC5C,OAAA;sBAAQuD,KAAK,EAAC,SAAS;sBAAApB,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN5C,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAOkC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR5C,OAAA;oBACEuD,KAAK,EAAE3C,UAAW;oBAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CrB,SAAS,EAAC,kIAAkI;oBAAAC,QAAA,gBAE5InC,OAAA;sBAAQuD,KAAK,EAAC,OAAO;sBAAApB,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpC5C,OAAA;sBAAQuD,KAAK,EAAC,QAAQ;sBAAApB,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC5C,OAAA;sBAAQuD,KAAK,EAAC,OAAO;sBAAApB,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5C,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAIkC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnE5C,OAAA;gBAAKkC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,eAC/DnC,OAAA;kBAAKkC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,eAC/CnC,OAAA;oBACEkC,SAAS,EAAE,+EACTxB,WAAW,KAAK,SAAS,GAAG,4CAA4C,GACxEA,WAAW,KAAK,SAAS,GAAG,8EAA8E,GAC1GA,WAAW,KAAK,SAAS,GAAG,sCAAsC,GAClE,0CAA0C,IAE1CE,UAAU,KAAK,OAAO,GAAG,qBAAqB,GAC9CA,UAAU,KAAK,OAAO,GAAG,mBAAmB,GAC5C,qBAAqB,EACpB;oBAAAuB,QAAA,gBAEHnC,OAAA,CAACJ,KAAK;sBAACsC,SAAS,EAAC;oBAAc;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oBAEpC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5C,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAIkC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChF5C,OAAA;gBAAKkC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCnC,OAAA;kBAAKkC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDnC,OAAA,CAACH,UAAU;oBAACqC,SAAS,EAAC;kBAAqC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9D5C,OAAA;oBAAGkC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D5C,OAAA;oBAAGkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN5C,OAAA;kBAAKkC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDnC,OAAA,CAACF,OAAO;oBAACoC,SAAS,EAAC;kBAAqC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3D5C,OAAA;oBAAGkC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7D5C,OAAA;oBAAGkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN5C,OAAA;kBAAKkC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDnC,OAAA,CAACJ,KAAK;oBAACsC,SAAS,EAAC;kBAAqC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzD5C,OAAA;oBAAGkC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D5C,OAAA;oBAAGkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5C,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAKkC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnC,OAAA;gBAAIkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjE5C,OAAA;gBACE8C,OAAO,EAAEjB,eAAgB;gBACzBK,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,GAErH7B,MAAM,gBAAGN,OAAA,CAACN,KAAK;kBAACwC,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACP,IAAI;kBAACyC,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/EtC,MAAM,GAAG,SAAS,GAAG,WAAW;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5C,OAAA;cAAKkC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDnC,OAAA;gBAAKkC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCnC,OAAA;kBAAAmC,QAAA,EAAOjB,iBAAiB,CAAC;gBAAC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKkC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnC,OAAA;gBAAKkC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/DnC,OAAA;kBAAIkC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtE5C,OAAA;kBAAIkC,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7CnC,OAAA;oBAAAmC,QAAA,EAAI;kBAA4B;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrC5C,OAAA;oBAAAmC,QAAA,EAAI;kBAAsE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/E5C,OAAA;oBAAAmC,QAAA,EAAI;kBAAgD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD5C,OAAA;oBAAAmC,QAAA,EAAI;kBAAkE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3E5C,OAAA;oBAAAmC,QAAA,EAAI;kBAAwD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE5C,OAAA;oBAAAmC,QAAA,EAAI;kBAA6C;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtD5C,OAAA;oBAAAmC,QAAA,EAAI;kBAAuC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACL5C,OAAA;kBAAKkC,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,eAC9CnC,OAAA;oBAAGkC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBAClCnC,OAAA;sBAAAmC,QAAA,EAAQ;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,qBAAiB,EAAC,CAAAxC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgB,EAAE,KAAI,gBAAgB,EAAC,iEAC7E;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKkC,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnEnC,OAAA;kBAAIkC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChE5C,OAAA;kBAAGkC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GAAC,YAC3B,eAAAnC,OAAA;oBAAG8D,IAAI,EAAC,GAAG;oBAAC5B,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,OACjE,eAAA5C,OAAA;oBAAG8D,IAAI,EAAC,GAAG;oBAAC5B,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,oBAC5D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACvC,EAAA,CA/SIJ,kBAAkB;AAAA8D,EAAA,GAAlB9D,kBAAkB;AAiTxB,eAAeA,kBAAkB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}