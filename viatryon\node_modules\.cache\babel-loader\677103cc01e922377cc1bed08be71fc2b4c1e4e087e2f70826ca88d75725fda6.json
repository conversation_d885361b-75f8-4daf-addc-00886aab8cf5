{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\VirtualTryOn.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\n\n// Add CSS for range slider styling\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 60px;\n    height: 34px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.2);\n    transition: .4s;\n    border-radius: 34px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 26px;\n    width: 26px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .4s;\n    border-radius: 50%;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(26px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  .switch-label {\n    font-size: 12px;\n    font-weight: 700;\n    color: white;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);\n    margin-bottom: 8px;\n    letter-spacing: 0.5px;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\nconst VirtualTryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n  const [showDisclaimerPopup, setShowDisclaimerPopup] = useState(true);\n  const [isUsingModelImage, setIsUsingModelImage] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(50); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkDevice();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Add new watch measurement function\n  const measureWatchFromImage = async (imagePath, wristWidthMm) => {\n    try {\n      // Create a canvas to load and process the image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      // Load image and wait for it to be ready\n      await new Promise((resolve, reject) => {\n        img.onload = resolve;\n        img.onerror = reject;\n        img.src = imagePath;\n      });\n\n      // Set canvas size to match image\n      canvas.width = img.width;\n      canvas.height = img.height;\n\n      // Draw image to canvas\n      ctx.drawImage(img, 0, 0);\n\n      // Get image data for processing\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Convert to grayscale for edge detection\n      const gray = new Uint8ClampedArray(canvas.width * canvas.height);\n      for (let i = 0; i < data.length; i += 4) {\n        gray[i / 4] = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;\n      }\n\n      // Edge detection using Sobel operator\n      const edges = new Uint8ClampedArray(canvas.width * canvas.height);\n      for (let y = 1; y < canvas.height - 1; y++) {\n        for (let x = 1; x < canvas.width - 1; x++) {\n          const idx = y * canvas.width + x;\n          const gx = -gray[idx - 1 - canvas.width] + gray[idx + 1 - canvas.width] + -2 * gray[idx - 1] + 2 * gray[idx + 1] + -gray[idx - 1 + canvas.width] + gray[idx + 1 + canvas.width];\n          const gy = -gray[idx - canvas.width - 1] - 2 * gray[idx - canvas.width] - gray[idx - canvas.width + 1] + gray[idx + canvas.width - 1] + 2 * gray[idx + canvas.width] + gray[idx + canvas.width + 1];\n          edges[idx] = Math.min(255, Math.sqrt(gx * gx + gy * gy));\n        }\n      }\n\n      // Find the largest central contour (likely the dial)\n      const imgCenterX = canvas.width / 2;\n      let maxArea = 0;\n      let dialBounds = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n\n      // Contour finding\n      for (let y = 0; y < canvas.height; y++) {\n        for (let x = 0; x < canvas.width; x++) {\n          if (edges[y * canvas.width + x] > 128) {\n            let width = 0;\n            let height = 0;\n            let startX = x;\n            let startY = y;\n\n            // Find width\n            while (x < canvas.width && edges[y * canvas.width + x] > 128) {\n              width++;\n              x++;\n            }\n\n            // Find height\n            let tempY = y;\n            while (tempY < canvas.height && edges[tempY * canvas.width + startX] > 128) {\n              height++;\n              tempY++;\n            }\n\n            // Check if this is a central contour\n            const centerX = startX + width / 2;\n            if (Math.abs(centerX - imgCenterX) < canvas.width * 0.2) {\n              const area = width * height;\n              if (area > maxArea) {\n                maxArea = area;\n                dialBounds = {\n                  x: startX,\n                  y: startY,\n                  width,\n                  height\n                };\n              }\n            }\n          }\n        }\n      }\n\n      // Calculate measurements\n      const dialWidthPx = dialBounds.width;\n      const fullHeightPx = canvas.height;\n\n      // Calculate pixels per mm based on assumed dial size\n      const pxPerMm = dialWidthPx / ASSUMED_DIAL_SIZE;\n\n      // Calculate scale ratio for wrist width\n      const scaleRatio = wristWidthMm / ASSUMED_DIAL_SIZE;\n\n      // Calculate scaled dimensions\n      const scaledWidth = Math.round(canvas.width * scaleRatio);\n      const scaledHeight = Math.round(canvas.height * scaleRatio);\n      return {\n        dialWidthPx,\n        fullWatchHeightPx: fullHeightPx,\n        scaleRatio: Math.round(scaleRatio * 100) / 100,\n        scaledDimensions: [scaledWidth, scaledHeight],\n        pxPerMm: Math.round(pxPerMm * 100) / 100,\n        realDialSize: ASSUMED_DIAL_SIZE,\n        realWatchHeight: fullHeightPx / pxPerMm\n      };\n    } catch (error) {\n      console.error('Error measuring watch:', error);\n      return null;\n    }\n  };\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50,\n    // mm - average men's wrist width from top view\n    women: 45 // mm - average women's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender(gender);\n    setUserWristSize(gender === 'men' ? 50 : 45); // Set initial size based on gender\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"/imgs/watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"/imgs/watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"/imgs/watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"/imgs/watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"/imgs/watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"/imgs/watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"/imgs/bracelets_tryon/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"/imgs/bracelets_tryon/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"/imgs/bracelets_tryon/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"/imgs/bracelets_tryon/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"/imgs/bracelets_tryon/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"/imgs/bracelets_tryon/bracelet_6.png\"\n  }];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    img.onload = function () {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = r > 245 && g > 245 && b > 245 && Math.abs(r - g) < 10 && Math.abs(g - b) < 10 && !isNearEdge;\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n    img.onerror = function () {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40,\n        // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(false);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n    setIsUsingModelImage(false); // Reset model image state\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Handle Try On Model button click\n  const handleTryOnModel = () => {\n    if (!isCaptured) {\n      // Load the model hand image from public folder\n      const modelImagePath = '/imgs/hand/hand.png'; // Using the hand.png from public folder\n\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = modelImagePath;\n        capturedImageRef.current.style.display = 'block';\n\n        // Wait for image to load, then detect wrist and apply try-on logic\n        capturedImageRef.current.onload = () => {\n          // Simulate wrist detection on the model image\n          detectWristOnModelImage();\n        };\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(true);\n      setShowProductSelection(true);\n      setShowHandGuide(false);\n\n      // Set hand orientation for model (assume left hand for consistency)\n      setIsRightHand(false);\n    }\n  };\n\n  // Detect wrist position on model image and apply scaling\n  const detectWristOnModelImage = () => {\n    if (!capturedImageRef.current) return;\n    try {\n      // Create a canvas to analyze the model image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = capturedImageRef.current;\n      canvas.width = img.naturalWidth || 800;\n      canvas.height = img.naturalHeight || 600;\n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n      // For model image with closed palm, position the watch/bracelet on the palm area\n      // Since it's a closed hand, we need to detect the palm center rather than wrist\n      const palmPosition = detectPalmPositionOnModel(canvas);\n      if (palmPosition) {\n        console.log('Palm position detected on model image:', palmPosition);\n        // Store the palm position for product placement\n        window.modelPalmPosition = palmPosition;\n      } else {\n        console.warn('Could not detect palm position on model image');\n      }\n    } catch (error) {\n      console.warn('Model image palm detection error:', error);\n    }\n  };\n\n  // Detect palm position on closed hand model image\n  const detectPalmPositionOnModel = canvas => {\n    try {\n      const ctx = canvas.getContext('2d');\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // For a closed hand model, we need to find the wrist area where the watch/bracelet should go\n      // The wrist is typically at the bottom of the hand, where the arm connects\n\n      // Scan from bottom up to find the wrist area (narrowest part of the arm)\n      let wristY = null;\n      let wristX = canvas.width * 0.5; // Start from center\n      let minWidth = canvas.width;\n\n      // Scan from bottom 20% to 80% of image height\n      for (let y = Math.floor(canvas.height * 0.8); y > canvas.height * 0.2; y -= 5) {\n        let leftEdge = -1;\n        let rightEdge = -1;\n\n        // Find left and right edges of the arm/hand at this height\n        for (let x = 0; x < canvas.width; x++) {\n          const idx = (y * canvas.width + x) * 4;\n          const r = data[idx];\n          const g = data[idx + 1];\n          const b = data[idx + 2];\n          const alpha = data[idx + 3];\n\n          // Check if this is a skin-colored pixel (not background)\n          if (alpha > 128 && (r > 100 || g > 80 || b > 60)) {\n            if (leftEdge === -1) leftEdge = x;\n            rightEdge = x;\n          }\n        }\n        if (leftEdge !== -1 && rightEdge !== -1) {\n          const width = rightEdge - leftEdge;\n          if (width < minWidth && width > 20) {\n            // Must be reasonable width\n            minWidth = width;\n            wristY = y;\n            wristX = (leftEdge + rightEdge) / 2;\n          }\n        }\n      }\n      if (wristY !== null) {\n        // Found wrist position - return bigger area for model hand\n        return {\n          x: wristX - 80,\n          y: wristY - 40,\n          width: 160,\n          height: 80,\n          centerX: wristX,\n          centerY: wristY + 10,\n          // Slightly below wrist center\n          confidence: 0.8,\n          isModelHand: true\n        };\n      }\n      return null;\n    } catch (error) {\n      console.error('Error detecting wrist position on closed hand:', error);\n      // Return default wrist position with bigger size for model hand\n      return {\n        x: canvas.width * 0.5 - 80,\n        y: canvas.height * 0.85 - 40,\n        width: 160,\n        height: 80,\n        centerX: canvas.width * 0.5,\n        centerY: canvas.height * 0.85 + 10,\n        confidence: 0.7,\n        isModelHand: true\n      };\n    }\n  };\n\n  // Handle disclaimer popup close\n  const handleDisclaimerClose = () => {\n    setShowDisclaimerPopup(false);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n    // Show disclaimer popup when component first loads\n    setShowDisclaimerPopup(true);\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    setIsUsingModelImage(false); // Reset model image state\n    // Go back to home instead of just resetting\n    onBackToHome();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n\n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n\n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n\n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.desktopContainer,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.qrContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.qrTitle,\n        children: \"Scan QR Code to Try On\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.qrSubtitle,\n        children: \"Open this page on your mobile device to experience the virtual try-on feature\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.qrWrapper,\n        children: /*#__PURE__*/_jsxDEV(QRCodeSVG, {\n          value: \"https://www.viatryon.com/try-on/watches\",\n          size: 256,\n          level: \"H\",\n          includeMargin: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.qrLink,\n        children: \"https://www.viatryon.com/try-on/watches\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.homeBtn,\n        onClick: onBackToHome,\n        \"aria-label\": \"Home\",\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1185,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1184,\n    columnNumber: 5\n  }, this);\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return /*#__PURE__*/_jsxDEV(DesktopQRCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1210,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Update product selection panel JSX\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [showDisclaimerPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.disclaimerOverlay,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.disclaimerPopup,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.disclaimerContent,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.disclaimerTitle,\n            children: \"Before You Start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.disclaimerPoints,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: styles.disclaimerPoint,\n              children: \"Images shown are for demonstration purposes - actual size may vary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: styles.disclaimerPoint,\n              children: \"Position your wrist within the guide lines for better results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.disclaimerButton,\n            onClick: handleDisclaimerClose,\n            children: \"Got it\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1219,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1218,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"/\",\n      style: styles.backArrowBtn,\n      \"aria-label\": \"Back to Home\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1247,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.brandingContainerBetween,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/imgs/logo-only.png\",\n        alt: \"ViaTryon Logo\",\n        style: {\n          ...styles.brandingLogoTiny,\n          filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.brandingTextTiny,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            ...styles.poweredByTextTiny,\n            color: isUsingModelImage ? '#000000' : styles.poweredByTextTiny.color\n          },\n          children: \"Powered by\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            ...styles.viatryonTextTiny,\n            color: isUsingModelImage ? '#000000' : styles.viatryonTextTiny.color\n          },\n          children: \"ViaTryon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.cameraContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        style: styles.cameraFeed,\n        autoPlay: true,\n        playsInline: true,\n        muted: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        style: styles.capturedImage,\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1283,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: isMobile ? 10 : 20,\n          right: isMobile ? 10 : 20,\n          zIndex: 20,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: '8px',\n          padding: '12px',\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: '20px',\n          backdropFilter: 'blur(10px)',\n          WebkitBackdropFilter: 'blur(10px)',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n          border: '1px solid rgba(255, 255, 255, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch-label\",\n          children: \"Auto Capture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1308,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"switch-slider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1319,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1311,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1291,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.countdownDisplay,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownNumber,\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1329,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownText,\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1328,\n        columnNumber: 11\n      }, this), !isCaptured && !isCountdownActive && !isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.instructionContainer,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.instructionText,\n          children: \"Position your wrist within the guides\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1337,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1336,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessageSmall,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusTextSmall,\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusSubtextSmall,\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1347,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.statusText,\n            backgroundColor: 'rgba(45, 140, 136, 0.9)'\n          },\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1353,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1352,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1376,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1403,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1427,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1451,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1454,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1374,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1361,\n        columnNumber: 11\n      }, this), selectedProduct && isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.productPosition,\n          // Adjust positioning for model hand - move up/down and make bigger\n          // Change '52%' to move up (lower %) or down (higher %)\n          top: isUsingModelImage ? '52%' : '50%',\n          transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',\n          width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n          height: activeTab === 'Watches' ? (() => {\n            const defaultWristSize = DEFAULT_WRIST_SIZE;\n            const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n            if (isLargeWrist) {\n              // For large wrists, increase height by 40% to allow exceeding SVG height\n              const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n              return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n            }\n            return `${WATCH_HEIGHT}%`;\n          })() : `${BRACELET_HEIGHT}%`,\n          // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n          clipPath: (() => {\n            const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n            return activeTab === 'Watches' && isLargeWrist ? 'ellipse(220px 60px at 50% 50%)' : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE ? 'ellipse(220px 60px at 50% 50%)' : 'none';\n          })(),\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n            alt: \"Selected product\",\n            style: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              transform: activeTab === 'Bracelets' ? (() => {\n                // Use exact bracelet fitting logic with hand-based vertical rotation\n                const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 35})`;\n\n                // Apply realistic bracelet positioning based on hand detection\n                // Rotate vertically but in opposite directions for left vs right hand\n                if (isUsingModelImage) {\n                  // For left hand model: rotate vertically in opposite direction\n                  return `${baseTransform} scaleY(1)`; // Opposite of current scaleY(-1)\n                } else if (isRightHand) {\n                  // For right hand: rotate vertically one direction\n                  return `${baseTransform} scaleY(-1)`;\n                } else {\n                  // For left hand: rotate vertically opposite direction\n                  return `${baseTransform} scaleY(1)`;\n                }\n              })() : (_selectedProduct$dime => {\n                const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                const isLargeWrist = userGender === 'men' && adjustedWristSize >= 50 || userGender === 'women' && adjustedWristSize >= 45;\n                if (isLargeWrist) {\n                  // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                  const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                  const heightScale = 1 + sizeIncrease * 0.4; // 40% height increase\n                  const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                  return `scale(${WATCH_HEIGHT / 25 * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                }\n\n                // For smaller wrists, use the original working logic with SVG height constraint\n                return `scale(${Math.min(WATCH_HEIGHT / 25 * (adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : defaultWristSize / adjustedWristSize), 300 / (((_selectedProduct$dime = selectedProduct.dimensions) === null || _selectedProduct$dime === void 0 ? void 0 : _selectedProduct$dime.totalHeight) || 47) // Scale to match SVG height\n                )}) scaleX(${adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : 1})`;\n              })(),\n              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n            },\n            onLoad: e => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1504,\n            columnNumber: 15\n          }, this), activeTab === 'Watches' && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '-30px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              fontSize: '11px',\n              fontWeight: '600',\n              color: 'white',\n              backgroundColor: 'rgba(45, 140, 136, 0.9)',\n              padding: '3px 8px',\n              borderRadius: '12px',\n              whiteSpace: 'nowrap',\n              pointerEvents: 'none',\n              boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n              zIndex: 2\n            },\n            children: [selectedProduct.dialSize, \"mm\", userWristSize !== DEFAULT_WRIST_SIZE && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8,\n                marginLeft: '4px'\n              },\n              children: (() => {\n                const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n                let scalingPercentage;\n                if (userWristSize < DEFAULT_WRIST_SIZE) {\n                  // Realistic scaling for smaller wrists\n                  const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                  const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                  const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                  const moderateScaleFactor = 1 + clampedDifference / DEFAULT_WRIST_SIZE * 0.6;\n                  scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                } else {\n                  // Standard scaling for larger wrists\n                  scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                }\n\n                // Add debug indicator for large wrists\n                const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1577,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1559,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1496,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1465,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.cameraControls,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureButtonWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.captureBtn,\n            className: isMobile ? 'mobile-capture-btn' : '',\n            onClick: handleCapture,\n            \"aria-label\": \"Capture\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.captureInner,\n              className: isMobile ? 'mobile-inner-circle' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1621,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.buttonLabel,\n            children: \"Capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1623,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modelButtonWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modelBtn,\n            className: isMobile ? 'mobile-model-btn' : '',\n            onClick: handleTryOnModel,\n            \"aria-label\": \"Try on model\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19,13H16.5L15.5,15H14L16,11H13V7H11V11H8L10,15H8.5L7.5,13H5C4.46,13 4,13.46 4,14V16C4,16.54 4.46,17 5,17H6V19C6,20.11 6.9,21 8,21H16C17.11,21 18,20.11 18,19V17H19C19.54,17 20,16.54 20,16V14C20,13.46 19.54,13 19,13M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1635,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1634,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1628,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.buttonLabel,\n            children: \"Try Model\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1638,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1627,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1612,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.productArrowBtn,\n        onClick: () => setShowProductSelection(true),\n        \"aria-label\": \"Select Products\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1651,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1650,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1645,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1274,\n      columnNumber: 7\n    }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.wristSizeFloatingBtn,\n      className: isMobile ? 'mobile-btn' : '',\n      onClick: () => setShowWristSizeModal(true),\n      \"aria-label\": \"Adjust wrist size\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1668,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1667,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.wristSizeText,\n        children: [userWristSize, \"mm\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1670,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1661,\n      columnNumber: 9\n    }, this), showWristSizeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalOverlay,\n      onClick: () => setShowWristSizeModal(false),\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeModal,\n        onClick: e => e.stopPropagation(),\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.modalTitle,\n            children: \"Adjust Wrist Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1687,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modalCloseBtn,\n            onClick: () => setShowWristSizeModal(false),\n            \"aria-label\": \"Close\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1694,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1693,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1688,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1686,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalContent,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.sliderLabel,\n              children: [\"Wrist Size: \", userWristSize, \"mm\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1702,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: userGender === 'men' ? \"40\" : \"35\",\n              max: userGender === 'men' ? \"65\" : \"60\",\n              value: userWristSize,\n              onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n              style: styles.slider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1705,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.sliderLabels,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: userGender === 'men' ? \"40mm\" : \"35mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: userGender === 'men' ? \"65mm\" : \"60mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1715,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1713,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.presetButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(DEFAULT_WRIST_SIZE),\n                children: [\"Average (\", DEFAULT_WRIST_SIZE, \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1720,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(MIN_WRIST_SIZE),\n                children: [\"Small (\", MIN_WRIST_SIZE, \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(MAX_WRIST_SIZE),\n                children: [\"Large (\", MAX_WRIST_SIZE, \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1719,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1701,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1699,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1681,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1676,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: panelRef,\n      style: {\n        ...styles.productSelection,\n        transform: `translateY(${panelPosition}px)`,\n        touchAction: 'none'\n      },\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dragHandle,\n        \"aria-hidden\": \"true\",\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1761,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1769,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1778,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1768,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1807,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1816,\n                columnNumber: 21\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1818,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1815,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1796,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1788,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1747,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1215,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(VirtualTryon, \"jW8WQ7V9sk6mWLwekKXDgt5TDLQ=\");\n_c = VirtualTryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '25vw',\n    // width controlled\n    aspectRatio: '1 / 1.6',\n    // maintain height-to-width ratio (adjust as needed)\n    minWidth: '100px',\n    minHeight: '160px',\n    // fallback for unsupported aspect-ratio\n    pointerEvents: 'none'\n  },\n  captureBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  captureInner: {\n    width: '40px',\n    height: '40px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n  // Disclaimer Popup Styles\n  disclaimerOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 1000,\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  disclaimerPopup: {\n    backgroundColor: 'white',\n    borderRadius: '20px',\n    padding: '0',\n    maxWidth: '400px',\n    width: '90%',\n    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.2)'\n  },\n  disclaimerContent: {\n    padding: '32px 24px',\n    textAlign: 'center'\n  },\n  disclaimerTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '24px',\n    margin: '0 0 24px 0'\n  },\n  disclaimerPoints: {\n    marginBottom: '32px'\n  },\n  disclaimerPoint: {\n    fontSize: '16px',\n    color: '#333',\n    lineHeight: '1.6',\n    marginBottom: '16px',\n    margin: '0 0 16px 0',\n    textAlign: 'center'\n  },\n  disclaimerButton: {\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    border: 'none',\n    borderRadius: '12px',\n    padding: '14px 32px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  // Clean Instruction Styles\n  instructionContainer: {\n    position: 'absolute',\n    top: '28%',\n    // moved further down from 20%\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 19,\n    pointerEvents: 'none'\n  },\n  instructionText: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: 'white',\n    textAlign: 'center',\n    padding: '8px 16px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  },\n  // Add new styles for smaller status message\n  statusMessageSmall: {\n    position: 'absolute',\n    top: '13%',\n    // move up so it's above the SVG shape\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '6px 12px',\n    borderRadius: '10px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n  },\n  statusTextSmall: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.8)',\n    padding: '6px 12px',\n    borderRadius: '15px',\n    marginBottom: '4px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtextSmall: {\n    fontSize: '10px',\n    fontWeight: '400',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '10px'\n  },\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)',\n    // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none'\n  },\n  brandingLogoTangiblee: {\n    width: '38px',\n    height: '38px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.1\n  },\n  poweredByText: {\n    fontSize: '15px',\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '1px'\n  },\n  viatryonText: {\n    fontSize: '28px',\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0'\n  },\n  // Camera Controls - Two buttons side by side\n  cameraControls: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '40px',\n    zIndex: 15\n  },\n  captureButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  modelButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  buttonLabel: {\n    fontSize: '12px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '8px',\n    pointerEvents: 'none'\n  },\n  modelBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  // Product Arrow Button - Shows after capture/model mode\n  productArrowBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 15\n  },\n  // Back Arrow Button - Top Left\n  backArrowBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    width: '44px',\n    height: '44px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    textDecoration: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 20\n  },\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '16px',\n    // Moved more to the left\n    display: 'flex',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)',\n    // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none'\n  },\n  brandingLogoTangiblee: {\n    width: '45px',\n    // Made logo slightly bigger\n    height: '45px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.2 // Increased line height for more vertical space\n  },\n  poweredByText: {\n    fontSize: '16px',\n    // Made text slightly bigger\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '2px' // Increased spacing between texts\n  },\n  viatryonText: {\n    fontSize: '28px',\n    // Made text bigger\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0'\n  },\n  // Back Arrow Button - Top Left\n  backArrowBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    width: '44px',\n    height: '44px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 20\n  },\n  // Tiny Branding - Between back arrow and autocapture switch\n  brandingContainerBetween: {\n    position: 'absolute',\n    top: '25px',\n    left: '65px',\n    // Moved more to the left\n    display: 'flex',\n    alignItems: 'center',\n    gap: '6px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)',\n    // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none'\n  },\n  brandingLogoTiny: {\n    width: '35px',\n    // Made logo bigger\n    height: '35px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle'\n  },\n  brandingTextTiny: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.2 // Increased line height for more vertical space\n  },\n  poweredByTextTiny: {\n    fontSize: '14px',\n    // Made text bigger\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '2px' // Increased spacing between texts\n  },\n  viatryonTextTiny: {\n    fontSize: '14px',\n    // Made text bigger\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0'\n  }\n};\nexport default VirtualTryon;\nvar _c;\n$RefreshReg$(_c, \"VirtualTryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "QRCodeSVG", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sliderCSS", "document", "getElementById", "styleElement", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "VirtualTryon", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "isDesktop", "setIsDesktop", "showDisclaimerPopup", "setShowDisclaimerPopup", "isUsingModelImage", "setIsUsingModelImage", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeModal", "setShowWristSizeModal", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "panelRef", "checkDevice", "isMobileDevice", "window", "innerWidth", "setVH", "vh", "innerHeight", "documentElement", "style", "setProperty", "addEventListener", "setTimeout", "removeEventListener", "DEFAULT_WRIST_SIZE", "MIN_WRIST_SIZE", "MAX_WRIST_SIZE", "ASSUMED_DIAL_SIZE", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "measureWatchFromImage", "imagePath", "wristWidthMm", "canvas", "ctx", "getContext", "img", "Image", "Promise", "resolve", "reject", "onload", "onerror", "src", "width", "height", "drawImage", "imageData", "getImageData", "data", "gray", "Uint8ClampedArray", "i", "length", "edges", "y", "x", "idx", "gx", "gy", "Math", "min", "sqrt", "imgCenterX", "maxArea", "dialBounds", "startX", "tempY", "centerX", "abs", "area", "dialWidthPx", "fullHeightPx", "pxPerMm", "scaleRatio", "scaledWidth", "round", "scaledHeight", "fullWatchHeightPx", "scaledDimensions", "realDialSize", "realWatchHeight", "error", "console", "DEFAULT_WRIST_SIZES", "men", "women", "WRIST_SIZE_OFFSET", "MIN_ADJUSTED_WRIST_SIZE", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "adjustedWristSize", "max", "inverseWristSizeRatio", "mmToSvgScale", "watchWidthSvg", "totalWidth", "watchHeightSvg", "totalHeight", "dialDiameterSvg", "dialDiameter", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "scale", "realWidth", "realHeight", "caseDiameter", "wristSizeRatio", "handleGenderChange", "gender", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "svgHeight", "watchHeight", "scaleToFitHeight", "type", "watches", "name", "path", "caseThickness", "dialSize", "bracelets", "initCamera", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "display", "log", "<PERSON><PERSON><PERSON><PERSON>", "videoWidth", "videoHeight", "toDataURL", "removeBackground", "imgElement", "productType", "crossOrigin", "naturalWidth", "naturalHeight", "edgePixels", "Set", "r", "g", "b", "isEdge", "dy", "dx", "neighborIdx", "nr", "ng", "nb", "colorDiff", "add", "pixelIndex", "brightness", "isNearEdge", "has", "isPureWhite", "putImageData", "filter", "mixBlendMode", "opacity", "e", "warn", "detectHandOrientation", "random", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "dimensions", "handleCapture", "capturedDataUrl", "handleBack", "handleWristSizeChange", "size", "handleTabChange", "tabName", "handleProductSelect", "product", "handleTryOnModel", "modelImagePath", "detectWristOnModelImage", "palmPosition", "detectPalmPositionOnModel", "modelPalmPosition", "wristY", "wristX", "min<PERSON><PERSON><PERSON>", "floor", "leftEdge", "rightEdge", "alpha", "centerY", "confidence", "isModelHand", "handleDisclaimerClose", "interval", "setInterval", "clearInterval", "countdownInterval", "prev", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "handleTouchStart", "touches", "clientY", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "handleClickOutside", "target", "closest", "DesktopQRCode", "styles", "desktopContainer", "children", "qr<PERSON><PERSON><PERSON>", "qrTitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "qrSubtitle", "qrWrapper", "value", "level", "<PERSON><PERSON><PERSON><PERSON>", "qrLink", "homeBtn", "onClick", "container", "disclaimer<PERSON><PERSON><PERSON>", "disclaimerPopup", "disclaimerContent", "disclaimerTitle", "disclaimerPoints", "disclaimerPoint", "disclaimer<PERSON><PERSON><PERSON>", "href", "backArrowBtn", "viewBox", "fill", "d", "brandingContainerBetween", "alt", "brandingLogoTiny", "brandingTextTiny", "poweredByTextTiny", "color", "viatryonTextTiny", "cameraContainer", "ref", "cameraFeed", "autoPlay", "playsInline", "muted", "capturedImage", "position", "top", "right", "zIndex", "flexDirection", "alignItems", "gap", "padding", "backgroundColor", "borderRadius", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "boxShadow", "border", "className", "checked", "onChange", "disabled", "countdownDisplay", "countdownNumber", "countdownText", "instructionContainer", "instructionText", "statusMessageSmall", "statusTextSmall", "statusSubtextSmall", "statusMessage", "statusText", "handGuide", "xmlns", "stroke", "strokeWidth", "strokeLinecap", "rx", "cx", "cy", "textAnchor", "fontSize", "fontWeight", "productPosition", "transform", "isLargeWrist", "sizeIncrease", "clipPath", "overflow", "justifyContent", "objectFit", "baseTransform", "_selectedProduct$dime", "heightScale", "widthScale", "onLoad", "bottom", "left", "whiteSpace", "pointerEvents", "marginLeft", "scalingPercentage", "sizeDifference", "maxSizeDifference", "clampedDifference", "moderateScaleFactor", "toFixed", "debugSuffix", "cameraControls", "captureButtonWrapper", "captureBtn", "captureInner", "buttonLabel", "modelButtonWrapper", "modelBtn", "productArrowBtn", "wristSizeFloatingBtn", "wristSizeText", "modalOverlay", "wristSizeModal", "stopPropagation", "modalHeader", "modalTitle", "modalCloseBtn", "modalContent", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "parseInt", "slider", "slider<PERSON><PERSON><PERSON>", "presetButtons", "presetButton", "productSelection", "touchAction", "role", "onTouchStart", "onTouchMove", "onTouchEnd", "dragHandle", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "title", "productImage", "onError", "productLabel", "productName", "productSize", "_c", "fontFamily", "WebkitTapHighlightColor", "WebkitOverflowScrolling", "flex", "WebkitTransform", "cursor", "transition", "outline", "backBtn", "switchContainer", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "letterSpacing", "textAlign", "marginBottom", "animation", "statusSubtext", "max<PERSON><PERSON><PERSON>", "WebkitFilter", "aspectRatio", "minHeight", "resetBtn", "maxHeight", "borderBottom", "overflowY", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "lineHeight", "genderSelection", "genderButton", "genderButtonActive", "sizeChange", "background", "WebkitAppearance", "appearance", "continueButton", "borderTopLeftRadius", "borderTopRightRadius", "<PERSON><PERSON><PERSON><PERSON>", "userSelect", "WebkitUserSelect", "closeBtn", "gridTemplateColumns", "paddingBottom", "scrollbarWidth", "scrollbarColor", "textOverflow", "wordBreak", "brandingContainerTangiblee", "brandingLogoTangiblee", "verticalAlign", "brandingTextStacked", "poweredByText", "viatryonText", "textDecoration", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/VirtualTryOn.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\n\n// Add CSS for range slider styling\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 60px;\n    height: 34px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.2);\n    transition: .4s;\n    border-radius: 34px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 26px;\n    width: 26px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .4s;\n    border-radius: 50%;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(26px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  .switch-label {\n    font-size: 12px;\n    font-weight: 700;\n    color: white;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);\n    margin-bottom: 8px;\n    letter-spacing: 0.5px;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\n\nconst VirtualTryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n  const [showDisclaimerPopup, setShowDisclaimerPopup] = useState(true);\n  const [isUsingModelImage, setIsUsingModelImage] = useState(false);\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'\n  const [userWristSize, setUserWristSize] = useState(50); // Default men's wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkDevice();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Add new watch measurement function\n  const measureWatchFromImage = async (imagePath, wristWidthMm) => {\n    try {\n      // Create a canvas to load and process the image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n      \n      // Load image and wait for it to be ready\n      await new Promise((resolve, reject) => {\n        img.onload = resolve;\n        img.onerror = reject;\n        img.src = imagePath;\n      });\n\n      // Set canvas size to match image\n      canvas.width = img.width;\n      canvas.height = img.height;\n      \n      // Draw image to canvas\n      ctx.drawImage(img, 0, 0);\n      \n      // Get image data for processing\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n      \n      // Convert to grayscale for edge detection\n      const gray = new Uint8ClampedArray(canvas.width * canvas.height);\n      for (let i = 0; i < data.length; i += 4) {\n        gray[i/4] = (data[i] * 0.299 + data[i+1] * 0.587 + data[i+2] * 0.114);\n      }\n      \n      // Edge detection using Sobel operator\n      const edges = new Uint8ClampedArray(canvas.width * canvas.height);\n      for (let y = 1; y < canvas.height - 1; y++) {\n        for (let x = 1; x < canvas.width - 1; x++) {\n          const idx = y * canvas.width + x;\n          const gx = \n            -gray[idx - 1 - canvas.width] + gray[idx + 1 - canvas.width] +\n            -2 * gray[idx - 1] + 2 * gray[idx + 1] +\n            -gray[idx - 1 + canvas.width] + gray[idx + 1 + canvas.width];\n          const gy = \n            -gray[idx - canvas.width - 1] - 2 * gray[idx - canvas.width] - gray[idx - canvas.width + 1] +\n            gray[idx + canvas.width - 1] + 2 * gray[idx + canvas.width] + gray[idx + canvas.width + 1];\n          edges[idx] = Math.min(255, Math.sqrt(gx * gx + gy * gy));\n        }\n      }\n      \n      // Find the largest central contour (likely the dial)\n      const imgCenterX = canvas.width / 2;\n      let maxArea = 0;\n      let dialBounds = { x: 0, y: 0, width: 0, height: 0 };\n      \n      // Contour finding\n      for (let y = 0; y < canvas.height; y++) {\n        for (let x = 0; x < canvas.width; x++) {\n          if (edges[y * canvas.width + x] > 128) {\n            let width = 0;\n            let height = 0;\n            let startX = x;\n            let startY = y;\n            \n            // Find width\n            while (x < canvas.width && edges[y * canvas.width + x] > 128) {\n              width++;\n              x++;\n            }\n            \n            // Find height\n            let tempY = y;\n            while (tempY < canvas.height && edges[tempY * canvas.width + startX] > 128) {\n              height++;\n              tempY++;\n            }\n            \n            // Check if this is a central contour\n            const centerX = startX + width / 2;\n            if (Math.abs(centerX - imgCenterX) < canvas.width * 0.2) {\n              const area = width * height;\n              if (area > maxArea) {\n                maxArea = area;\n                dialBounds = { x: startX, y: startY, width, height };\n              }\n            }\n          }\n        }\n      }\n      \n      // Calculate measurements\n      const dialWidthPx = dialBounds.width;\n      const fullHeightPx = canvas.height;\n      \n      // Calculate pixels per mm based on assumed dial size\n      const pxPerMm = dialWidthPx / ASSUMED_DIAL_SIZE;\n      \n      // Calculate scale ratio for wrist width\n      const scaleRatio = wristWidthMm / ASSUMED_DIAL_SIZE;\n      \n      // Calculate scaled dimensions\n      const scaledWidth = Math.round(canvas.width * scaleRatio);\n      const scaledHeight = Math.round(canvas.height * scaleRatio);\n      \n      return {\n        dialWidthPx,\n        fullWatchHeightPx: fullHeightPx,\n        scaleRatio: Math.round(scaleRatio * 100) / 100,\n        scaledDimensions: [scaledWidth, scaledHeight],\n        pxPerMm: Math.round(pxPerMm * 100) / 100,\n        realDialSize: ASSUMED_DIAL_SIZE,\n        realWatchHeight: fullHeightPx / pxPerMm\n      };\n    } catch (error) {\n      console.error('Error measuring watch:', error);\n      return null;\n    }\n  };\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50,    // mm - average men's wrist width from top view\n    women: 45   // mm - average women's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n    \n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender(gender);\n    setUserWristSize(gender === 'men' ? 50 : 45); // Set initial size based on gender\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"/imgs/watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"/imgs/watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"/imgs/watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"/imgs/watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"/imgs/watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"/imgs/watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"/imgs/bracelets_tryon/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"/imgs/bracelets_tryon/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"/imgs/bracelets_tryon/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"/imgs/bracelets_tryon/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"/imgs/bracelets_tryon/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"/imgs/bracelets_tryon/bracelet_6.png\" }\n  ];\n\n  // Initialize camera\n  const initCamera = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current) return null;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Improved background remover that preserves light colors\n  const removeBackground = (imgElement, productType = 'watch') => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n\n    img.onload = function() {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = img.naturalWidth;\n      canvas.height = img.naturalHeight;\n      ctx.drawImage(img, 0, 0);\n\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n\n        // First pass: Identify edge pixels to preserve product boundaries\n        const edgePixels = new Set();\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n            const r = data[idx];\n            const g = data[idx + 1];\n            const b = data[idx + 2];\n\n            // Check surrounding pixels for edge detection\n            let isEdge = false;\n            for (let dy = -1; dy <= 1; dy++) {\n              for (let dx = -1; dx <= 1; dx++) {\n                if (dx === 0 && dy === 0) continue;\n                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;\n                const nr = data[neighborIdx];\n                const ng = data[neighborIdx + 1];\n                const nb = data[neighborIdx + 2];\n\n                // If there's a significant color difference, it's an edge\n                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);\n                if (colorDiff > 60) {\n                  isEdge = true;\n                  break;\n                }\n              }\n              if (isEdge) break;\n            }\n\n            if (isEdge) {\n              edgePixels.add(idx / 4);\n            }\n          }\n        }\n\n        // Second pass: Remove only white background while preserving all product colors\n        for (let i = 0; i < data.length; i += 4) {\n          const r = data[i];\n          const g = data[i + 1];\n          const b = data[i + 2];\n          const pixelIndex = i / 4;\n\n          // Calculate color properties\n          const brightness = (r + g + b) / 3;\n\n          // Check if pixel is near edges (preserve product boundaries)\n          const isNearEdge = edgePixels.has(pixelIndex);\n\n          // Only remove pure white or near-white pixels that aren't edges\n          const isPureWhite = (\n            r > 245 &&\n            g > 245 &&\n            b > 245 &&\n            Math.abs(r - g) < 10 &&\n            Math.abs(g - b) < 10 &&\n            !isNearEdge\n          );\n\n          // Remove background if it's pure white and not an edge\n          if (isPureWhite) {\n            data[i + 3] = 0; // Make fully transparent\n          } else if (brightness > 240 && !isNearEdge) {\n            // For very bright but not pure white, reduce opacity slightly\n            data[i + 3] = Math.max(0, data[i + 3] - 50);\n          }\n        }\n\n        ctx.putImageData(imageData, 0, 0);\n        imgElement.src = canvas.toDataURL('image/png');\n\n        // Apply product-specific styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n\n      } catch (e) {\n        console.warn('Canvas operation failed:', e);\n        // Fallback styling that preserves all colors\n        imgElement.style.filter = 'none';\n        imgElement.style.mixBlendMode = 'normal';\n        imgElement.style.opacity = '1';\n      }\n    };\n\n    img.onerror = function() {\n      console.warn('Image loading failed');\n      // Fallback styling\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n    };\n\n    img.src = imgElement.src;\n  };\n\n  // Placeholder for hand detection\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes\n    return Math.random() > 0.5;\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40, // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      setIsUsingModelImage(false);\n      setShowProductSelection(true); // Show products immediately\n      setShowHandGuide(false);\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      setShowProductSelection(true);\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n    setIsUsingModelImage(false); // Reset model image state\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Handle Try On Model button click\n  const handleTryOnModel = () => {\n    if (!isCaptured) {\n      // Load the model hand image from public folder\n      const modelImagePath = '/imgs/hand/hand.png'; // Using the hand.png from public folder\n\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = modelImagePath;\n        capturedImageRef.current.style.display = 'block';\n\n        // Wait for image to load, then detect wrist and apply try-on logic\n        capturedImageRef.current.onload = () => {\n          // Simulate wrist detection on the model image\n          detectWristOnModelImage();\n        };\n      }\n\n      setIsCaptured(true);\n      setIsUsingModelImage(true);\n      setShowProductSelection(true);\n      setShowHandGuide(false);\n\n      // Set hand orientation for model (assume left hand for consistency)\n      setIsRightHand(false);\n    }\n  };\n\n  // Detect wrist position on model image and apply scaling\n  const detectWristOnModelImage = () => {\n    if (!capturedImageRef.current) return;\n\n    try {\n      // Create a canvas to analyze the model image\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = capturedImageRef.current;\n\n      canvas.width = img.naturalWidth || 800;\n      canvas.height = img.naturalHeight || 600;\n      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n      // For model image with closed palm, position the watch/bracelet on the palm area\n      // Since it's a closed hand, we need to detect the palm center rather than wrist\n      const palmPosition = detectPalmPositionOnModel(canvas);\n\n      if (palmPosition) {\n        console.log('Palm position detected on model image:', palmPosition);\n        // Store the palm position for product placement\n        window.modelPalmPosition = palmPosition;\n      } else {\n        console.warn('Could not detect palm position on model image');\n      }\n\n    } catch (error) {\n      console.warn('Model image palm detection error:', error);\n    }\n  };\n\n  // Detect palm position on closed hand model image\n  const detectPalmPositionOnModel = (canvas) => {\n    try {\n      const ctx = canvas.getContext('2d');\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // For a closed hand model, we need to find the wrist area where the watch/bracelet should go\n      // The wrist is typically at the bottom of the hand, where the arm connects\n\n      // Scan from bottom up to find the wrist area (narrowest part of the arm)\n      let wristY = null;\n      let wristX = canvas.width * 0.5; // Start from center\n      let minWidth = canvas.width;\n\n      // Scan from bottom 20% to 80% of image height\n      for (let y = Math.floor(canvas.height * 0.8); y > canvas.height * 0.2; y -= 5) {\n        let leftEdge = -1;\n        let rightEdge = -1;\n\n        // Find left and right edges of the arm/hand at this height\n        for (let x = 0; x < canvas.width; x++) {\n          const idx = (y * canvas.width + x) * 4;\n          const r = data[idx];\n          const g = data[idx + 1];\n          const b = data[idx + 2];\n          const alpha = data[idx + 3];\n\n          // Check if this is a skin-colored pixel (not background)\n          if (alpha > 128 && (r > 100 || g > 80 || b > 60)) {\n            if (leftEdge === -1) leftEdge = x;\n            rightEdge = x;\n          }\n        }\n\n        if (leftEdge !== -1 && rightEdge !== -1) {\n          const width = rightEdge - leftEdge;\n          if (width < minWidth && width > 20) { // Must be reasonable width\n            minWidth = width;\n            wristY = y;\n            wristX = (leftEdge + rightEdge) / 2;\n          }\n        }\n      }\n\n      if (wristY !== null) {\n        // Found wrist position - return bigger area for model hand\n        return {\n          x: wristX - 80,\n          y: wristY - 40,\n          width: 160,\n          height: 80,\n          centerX: wristX,\n          centerY: wristY + 10, // Slightly below wrist center\n          confidence: 0.8,\n          isModelHand: true\n        };\n      }\n\n      return null;\n\n    } catch (error) {\n      console.error('Error detecting wrist position on closed hand:', error);\n      // Return default wrist position with bigger size for model hand\n      return {\n        x: canvas.width * 0.5 - 80,\n        y: canvas.height * 0.85 - 40,\n        width: 160,\n        height: 80,\n        centerX: canvas.width * 0.5,\n        centerY: canvas.height * 0.85 + 10,\n        confidence: 0.7,\n        isModelHand: true\n      };\n    }\n  };\n\n  // Handle disclaimer popup close\n  const handleDisclaimerClose = () => {\n    setShowDisclaimerPopup(false);\n  };\n\n  // Initialize camera on component mount\n  useEffect(() => {\n    initCamera();\n    // Show disclaimer popup when component first loads\n    setShowDisclaimerPopup(true);\n  }, []);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    setIsUsingModelImage(false); // Reset model image state\n    // Go back to home instead of just resetting\n    onBackToHome();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Add touch gesture handlers\n  const handleTouchStart = (e) => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n\n  const handleTouchMove = (e) => {\n    if (!isDragging) return;\n    \n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    \n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    \n    setIsDragging(false);\n    \n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    \n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = (e) => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    \n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => (\n    <div style={styles.desktopContainer}>\n      <div style={styles.qrContainer}>\n        <h2 style={styles.qrTitle}>Scan QR Code to Try On</h2>\n        <p style={styles.qrSubtitle}>Open this page on your mobile device to experience the virtual try-on feature</p>\n        <div style={styles.qrWrapper}>\n          <QRCodeSVG\n            value=\"https://www.viatryon.com/try-on/watches\"\n            size={256}\n            level=\"H\"\n            includeMargin={true}\n          />\n        </div>\n        <p style={styles.qrLink}>https://www.viatryon.com/try-on/watches</p>\n        <button\n          style={styles.homeBtn}\n          onClick={onBackToHome}\n          aria-label=\"Home\"\n        >\n          ← Back to Home\n        </button>\n      </div>\n    </div>\n  );\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return <DesktopQRCode />;\n  }\n\n  // Update product selection panel JSX\n  return (\n    <div style={styles.container}>\n      {/* Disclaimer Popup */}\n      {showDisclaimerPopup && (\n        <div style={styles.disclaimerOverlay}>\n          <div style={styles.disclaimerPopup}>\n            <div style={styles.disclaimerContent}>\n              <h3 style={styles.disclaimerTitle}>Before You Start</h3>\n              <div style={styles.disclaimerPoints}>\n                <p style={styles.disclaimerPoint}>\n                  Images shown are for demonstration purposes - actual size may vary\n                </p>\n                <p style={styles.disclaimerPoint}>\n                  Position your wrist within the guide lines for better results\n                </p>\n              </div>\n              <button\n                style={styles.disclaimerButton}\n                onClick={handleDisclaimerClose}\n              >\n                Got it\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Back Arrow Button - Top Left */}\n      <a\n        href=\"/\"\n        style={styles.backArrowBtn}\n        aria-label=\"Back to Home\"\n      >\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n          <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z\"/>\n        </svg>\n      </a>\n\n      {/* Powered by ViaTryon Branding - Between back arrow and autocapture */}\n      <div style={styles.brandingContainerBetween}>\n        <img\n          src=\"/imgs/logo-only.png\"\n          alt=\"ViaTryon Logo\"\n          style={{\n            ...styles.brandingLogoTiny,\n            filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'\n          }}\n        />\n        <div style={styles.brandingTextTiny}>\n          <span style={{\n            ...styles.poweredByTextTiny,\n            color: isUsingModelImage ? '#000000' : styles.poweredByTextTiny.color\n          }}>Powered by</span>\n          <span style={{\n            ...styles.viatryonTextTiny,\n            color: isUsingModelImage ? '#000000' : styles.viatryonTextTiny.color\n          }}>ViaTryon</span>\n        </div>\n      </div>\n\n      <div style={styles.cameraContainer}>\n        <video\n          ref={videoRef}\n          style={styles.cameraFeed}\n          autoPlay\n          playsInline\n          muted\n        />\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        <img\n          ref={capturedImageRef}\n          style={styles.capturedImage}\n          alt=\"Captured hand\"\n        />\n\n        {/* Autocapture Switch Button - Only visible when not captured */}\n        {!isCaptured && (\n          <div style={{\n            position: 'absolute',\n            top: isMobile ? 10 : 20,\n            right: isMobile ? 10 : 20,\n            zIndex: 20,\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            gap: '8px',\n            padding: '12px',\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: '20px',\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n            border: '1px solid rgba(255, 255, 255, 0.1)'\n          }}>\n            <label className=\"switch-label\">\n              Auto Capture\n            </label>\n            <label className=\"switch-container\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n              />\n              <span className=\"switch-slider\"></span>\n            </label>\n          </div>\n        )}\n\n\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div style={styles.countdownDisplay}>\n            <div style={styles.countdownNumber}>{countdown}</div>\n            <div style={styles.countdownText}>Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Simple Instruction Message - Clean and minimal */}\n        {!isCaptured && !isCountdownActive && !isAutoCaptureEnabled && (\n          <div style={styles.instructionContainer}>\n            <div style={styles.instructionText}>\n              Position your wrist within the guides\n            </div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessageSmall}>\n            <div style={styles.statusTextSmall}>Position your arm and wrist in the guide area</div>\n            <div style={styles.statusSubtextSmall}>Countdown will start automatically when detected</div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* Product Display - Only show after capture */}\n        {selectedProduct && isCaptured && (\n          <div style={{\n            ...styles.productPosition,\n            // Adjust positioning for model hand - move up/down and make bigger\n            // Change '52%' to move up (lower %) or down (higher %)\n            top: isUsingModelImage ? '52%' : '50%',\n            transform: isUsingModelImage ? 'translate(-50%, -50%) scale(1.3)' : 'translate(-50%, -50%)',\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches'\n              ? (() => {\n                  const defaultWristSize = DEFAULT_WRIST_SIZE;\n                  const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n\n                  if (isLargeWrist) {\n                    // For large wrists, increase height by 40% to allow exceeding SVG height\n                    const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n                    return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n                  }\n                  return `${WATCH_HEIGHT}%`;\n                })()\n              : `${BRACELET_HEIGHT}%`,\n            // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n            clipPath: (() => {\n              const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n              return activeTab === 'Watches' && isLargeWrist\n                ? 'ellipse(220px 60px at 50% 50%)'\n                : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE\n                  ? 'ellipse(220px 60px at 50% 50%)'\n                  : 'none';\n            })(),\n            overflow: 'hidden'\n          }}>\n            <div style={{\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <img\n                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                alt=\"Selected product\"\n                style={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'contain',\n                  transform: activeTab === 'Bracelets'\n                    ? (() => {\n                        // Use exact bracelet fitting logic with hand-based vertical rotation\n                        const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 35})`;\n\n                        // Apply realistic bracelet positioning based on hand detection\n                        // Rotate vertically but in opposite directions for left vs right hand\n                        if (isUsingModelImage) {\n                          // For left hand model: rotate vertically in opposite direction\n                          return `${baseTransform} scaleY(1)`; // Opposite of current scaleY(-1)\n                        } else if (isRightHand) {\n                          // For right hand: rotate vertically one direction\n                          return `${baseTransform} scaleY(-1)`;\n                        } else {\n                          // For left hand: rotate vertically opposite direction\n                          return `${baseTransform} scaleY(1)`;\n                        }\n                      })()\n                    : (() => {\n                        const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                        const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                        const isLargeWrist = (userGender === 'men' && adjustedWristSize >= 50) || \n                                            (userGender === 'women' && adjustedWristSize >= 45);\n\n                        if (isLargeWrist) {\n                          // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                          const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                          const heightScale = 1 + (sizeIncrease * 0.4); // 40% height increase\n                          const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                          return `scale(${(WATCH_HEIGHT / 25) * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                        }\n\n                        // For smaller wrists, use the original working logic with SVG height constraint\n                        return `scale(${Math.min(\n                          (WATCH_HEIGHT / 25) * (adjustedWristSize > defaultWristSize\n                            ? defaultWristSize / adjustedWristSize\n                            : defaultWristSize / adjustedWristSize),\n                          300 / (selectedProduct.dimensions?.totalHeight || 47) // Scale to match SVG height\n                        )}) scaleX(${adjustedWristSize > defaultWristSize\n                          ? defaultWristSize / adjustedWristSize\n                          : 1})`;\n                      })(),\n                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                }}\n                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}\n              />\n              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (\n                <div style={{\n                  position: 'absolute',\n                  bottom: '-30px',\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: 'white',\n                  backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                  padding: '3px 8px',\n                  borderRadius: '12px',\n                  whiteSpace: 'nowrap',\n                  pointerEvents: 'none',\n                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                  zIndex: 2\n                }}>\n                  {selectedProduct.dialSize}mm\n                  {userWristSize !== DEFAULT_WRIST_SIZE && (\n                    <span style={{\n                      fontSize: '10px',\n                      opacity: 0.8,\n                      marginLeft: '4px'\n                    }}>\n                      {(() => {\n                        const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n\n                        let scalingPercentage;\n                        if (userWristSize < DEFAULT_WRIST_SIZE) {\n                          // Realistic scaling for smaller wrists\n                          const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                          const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                          const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                          const moderateScaleFactor = 1 + (clampedDifference / DEFAULT_WRIST_SIZE) * 0.6;\n                          scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                        } else {\n                          // Standard scaling for larger wrists\n                          scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                        }\n\n                        // Add debug indicator for large wrists\n                        const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                        return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n                      })()}\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Camera Controls - Only visible when not captured */}\n        {!isCaptured && (\n          <div style={styles.cameraControls}>\n            {/* Camera-style Capture Button */}\n            <div style={styles.captureButtonWrapper}>\n              <button\n                style={styles.captureBtn}\n                className={isMobile ? 'mobile-capture-btn' : ''}\n                onClick={handleCapture}\n                aria-label=\"Capture\"\n              >\n                <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>\n              </button>\n              <span style={styles.buttonLabel}>Capture</span>\n            </div>\n\n            {/* Try On Model Button */}\n            <div style={styles.modelButtonWrapper}>\n              <button\n                style={styles.modelBtn}\n                className={isMobile ? 'mobile-model-btn' : ''}\n                onClick={handleTryOnModel}\n                aria-label=\"Try on model\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n                  <path d=\"M19,13H16.5L15.5,15H14L16,11H13V7H11V11H8L10,15H8.5L7.5,13H5C4.46,13 4,13.46 4,14V16C4,16.54 4.46,17 5,17H6V19C6,20.11 6.9,21 8,21H16C17.11,21 18,20.11 18,19V17H19C19.54,17 20,16.54 20,16V14C20,13.46 19.54,13 19,13M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2Z\"/>\n                </svg>\n              </button>\n              <span style={styles.buttonLabel}>Try Model</span>\n            </div>\n          </div>\n        )}\n\n        {/* Product Selection Arrow Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.productArrowBtn}\n            onClick={() => setShowProductSelection(true)}\n            aria-label=\"Select Products\"\n          >\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\"/>\n            </svg>\n          </button>\n        )}\n\n\n      </div>\n\n      {/* Mobile Wrist Size Button - Top right corner */}\n      {isCaptured && (\n        <button\n          style={styles.wristSizeFloatingBtn}\n          className={isMobile ? 'mobile-btn' : ''}\n          onClick={() => setShowWristSizeModal(true)}\n          aria-label=\"Adjust wrist size\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"white\">\n            <path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"/>\n          </svg>\n          <span style={styles.wristSizeText}>{userWristSize}mm</span>\n        </button>\n      )}\n\n      {/* Wrist Size Modal - Mobile-friendly popup */}\n      {showWristSizeModal && (\n        <div \n          style={styles.modalOverlay} \n          onClick={() => setShowWristSizeModal(false)}\n          className=\"modal-overlay\"\n        >\n          <div \n            style={styles.wristSizeModal} \n            onClick={(e) => e.stopPropagation()}\n            className=\"modal-content\"\n          >\n            <div style={styles.modalHeader}>\n              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>\n              <button\n                style={styles.modalCloseBtn}\n                onClick={() => setShowWristSizeModal(false)}\n                aria-label=\"Close\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n                </svg>\n              </button>\n            </div>\n\n            <div style={styles.modalContent}>\n              {/* Wrist Size Slider */}\n              <div style={styles.sliderContainer}>\n                <label style={styles.sliderLabel}>\n                  Wrist Size: {userWristSize}mm\n                </label>\n                <input\n                  type=\"range\"\n                  min={userGender === 'men' ? \"40\" : \"35\"}\n                  max={userGender === 'men' ? \"65\" : \"60\"}\n                  value={userWristSize}\n                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                  style={styles.slider}\n                />\n                <div style={styles.sliderLabels}>\n                  <span>{userGender === 'men' ? \"40mm\" : \"35mm\"}</span>\n                  <span>{userGender === 'men' ? \"65mm\" : \"60mm\"}</span>\n                </div>\n\n                {/* Quick Size Presets */}\n                <div style={styles.presetButtons}>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZE)}\n                  >\n                    Average ({DEFAULT_WRIST_SIZE}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(MIN_WRIST_SIZE)}\n                  >\n                    Small ({MIN_WRIST_SIZE}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(MAX_WRIST_SIZE)}\n                  >\n                    Large ({MAX_WRIST_SIZE}mm)\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          ref={panelRef}\n          style={{\n            ...styles.productSelection,\n            transform: `translateY(${panelPosition}px)`,\n            touchAction: 'none'\n          }}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n        >\n          <div \n            style={styles.dragHandle} \n            aria-hidden=\"true\"\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n          />\n          <div style={styles.productTabs}>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Watches' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Watches')}\n            >\n              Watches\n            </button>\n            <button\n              style={{\n                ...styles.tab,\n                ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n              }}\n              onClick={() => handleTabChange('Bracelets')}\n            >\n              Bracelets\n            </button>\n          </div>\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().map((product, index) => {\n              // Simple null check only\n              if (!product) return null;\n\n              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n              return (\n                <button\n                  key={index}\n                  style={{\n                    ...styles.productItem,\n                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                  }}\n                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                  onClick={() => handleProductSelect(product)}\n                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                >\n                  <img\n                    src={product.path}\n                    alt={product.name}\n                    style={styles.productImage}\n                    onError={(e) => {\n                      e.target.parentElement.style.display = 'none';\n                    }}\n                  />\n                  <div style={styles.productLabel}>\n                    <div style={styles.productName}>{product.name}</div>\n                    {activeTab === 'Watches' && product.caseDiameter && (\n                      <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                    )}\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\nproductPosition: {\n  position: 'absolute',\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)',\n  zIndex: 8,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  width: '25vw', // width controlled\n  aspectRatio: '1 / 1.6', // maintain height-to-width ratio (adjust as needed)\n  minWidth: '100px',\n  minHeight: '160px', // fallback for unsupported aspect-ratio\n  pointerEvents: 'none'\n}\n,\n  captureBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  captureInner: {\n    width: '40px',\n    height: '40px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n\n  // Disclaimer Popup Styles\n  disclaimerOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 1000,\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)'\n  },\n  disclaimerPopup: {\n    backgroundColor: 'white',\n    borderRadius: '20px',\n    padding: '0',\n    maxWidth: '400px',\n    width: '90%',\n    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.2)'\n  },\n  disclaimerContent: {\n    padding: '32px 24px',\n    textAlign: 'center'\n  },\n  disclaimerTitle: {\n    fontSize: '24px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '24px',\n    margin: '0 0 24px 0'\n  },\n  disclaimerPoints: {\n    marginBottom: '32px'\n  },\n  disclaimerPoint: {\n    fontSize: '16px',\n    color: '#333',\n    lineHeight: '1.6',\n    marginBottom: '16px',\n    margin: '0 0 16px 0',\n    textAlign: 'center'\n  },\n  disclaimerButton: {\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    border: 'none',\n    borderRadius: '12px',\n    padding: '14px 32px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  // Clean Instruction Styles\n  instructionContainer: {\n    position: 'absolute',\n    top: '28%', // moved further down from 20%\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 19,\n    pointerEvents: 'none'\n  },\n  instructionText: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: 'white',\n    textAlign: 'center',\n    padding: '8px 16px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  },\n\n  // Add new styles for smaller status message\n  statusMessageSmall: {\n    position: 'absolute',\n    top: '13%', // move up so it's above the SVG shape\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '6px 12px',\n    borderRadius: '10px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'\n  },\n  statusTextSmall: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.8)',\n    padding: '6px 12px',\n    borderRadius: '15px',\n    marginBottom: '4px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtextSmall: {\n    fontSize: '10px',\n    fontWeight: '400',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '10px'\n  },\n\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '12px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none',\n  },\n  brandingLogoTangiblee: {\n    width: '38px',\n    height: '38px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle',\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.1,\n  },\n  poweredByText: {\n    fontSize: '15px',\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '1px',\n  },\n  viatryonText: {\n    fontSize: '28px',\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0',\n  },\n\n  // Camera Controls - Two buttons side by side\n  cameraControls: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '40px',\n    zIndex: 15\n  },\n  captureButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  modelButtonWrapper: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '8px'\n  },\n  buttonLabel: {\n    fontSize: '12px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 3px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: '4px 8px',\n    borderRadius: '8px',\n    pointerEvents: 'none'\n  },\n  modelBtn: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n\n  // Product Arrow Button - Shows after capture/model mode\n  productArrowBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '60px',\n    height: '60px',\n    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 15\n  },\n\n  // Back Arrow Button - Top Left\n  backArrowBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    width: '44px',\n    height: '44px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    textDecoration: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 20\n  },\n\n  // Clean Branding - Tangiblee style\n  brandingContainerTangiblee: {\n    position: 'absolute',\n    top: '32px',\n    left: '16px', // Moved more to the left\n    display: 'flex',\n    gap: '14px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none',\n  },\n  brandingLogoTangiblee: {\n    width: '45px', // Made logo slightly bigger\n    height: '45px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle',\n  },\n  brandingTextStacked: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.2, // Increased line height for more vertical space\n  },\n  poweredByText: {\n    fontSize: '16px', // Made text slightly bigger\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '2px', // Increased spacing between texts\n  },\n  viatryonText: {\n    fontSize: '28px', // Made text bigger\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0',\n  },\n\n  // Back Arrow Button - Top Left\n  backArrowBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    width: '44px',\n    height: '44px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    zIndex: 20\n  },\n\n  // Tiny Branding - Between back arrow and autocapture switch\n  brandingContainerBetween: {\n    position: 'absolute',\n    top: '25px',\n    left: '65px', // Moved more to the left\n    display: 'flex',\n    alignItems: 'center',\n    gap: '6px',\n    zIndex: 15,\n    backgroundColor: 'rgba(0,0,0,0.0)', // fully transparent\n    padding: '0',\n    borderRadius: '0',\n    boxShadow: 'none',\n  },\n  brandingLogoTiny: {\n    width: '35px', // Made logo bigger\n    height: '35px',\n    objectFit: 'contain',\n    display: 'inline-block',\n    verticalAlign: 'middle',\n  },\n  brandingTextTiny: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'flex-start',\n    lineHeight: 1.2, // Increased line height for more vertical space\n  },\n  poweredByTextTiny: {\n    fontSize: '14px', // Made text bigger\n    color: 'white',\n    fontWeight: 400,\n    opacity: 0.85,\n    letterSpacing: '0.01em',\n    marginBottom: '2px', // Increased spacing between texts\n  },\n  viatryonTextTiny: {\n    fontSize: '14px', // Made text bigger\n    color: 'white',\n    fontWeight: 600,\n    letterSpacing: '0.01em',\n    marginTop: '0',\n  },\n};\n\nexport default VirtualTryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,cAAc,CAAC,CAAC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC,EAAE;EAC3F,MAAMC,YAAY,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,EAAE,GAAG,0BAA0B;EAC5CF,YAAY,CAACG,WAAW,GAAGN,SAAS;EACpCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;AACzC;AAEA,MAAMM,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC;EACA,MAAMC,QAAQ,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMmB,gBAAgB,GAAGnB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMoB,SAAS,GAAGpB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6D,MAAM,EAAEC,SAAS,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM+D,QAAQ,GAAG7D,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,MAAM+D,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,cAAc,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;MAC/C/B,WAAW,CAAC6B,cAAc,CAAC;MAC3B3B,YAAY,CAAC,CAAC2B,cAAc,CAAC;IAC/B,CAAC;;IAED;IACA,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAGH,MAAM,CAACI,WAAW,GAAG,IAAI;MACpC7D,QAAQ,CAAC8D,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,GAAGJ,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDL,WAAW,CAAC,CAAC;IACbI,KAAK,CAAC,CAAC;IAEPF,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCV,WAAW,CAAC,CAAC;MACbI,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEFF,MAAM,CAACQ,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDC,UAAU,CAAC,MAAM;QACfP,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACXF,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEZ,WAAW,CAAC;MACjDE,MAAM,CAACU,mBAAmB,CAAC,mBAAmB,EAAER,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMS,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC/B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;;EAE9B,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,YAAY,KAAK;IAC/D,IAAI;MACF;MACA,MAAMC,MAAM,GAAGlF,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMgF,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;;MAEvB;MACA,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACrCJ,GAAG,CAACK,MAAM,GAAGF,OAAO;QACpBH,GAAG,CAACM,OAAO,GAAGF,MAAM;QACpBJ,GAAG,CAACO,GAAG,GAAGZ,SAAS;MACrB,CAAC,CAAC;;MAEF;MACAE,MAAM,CAACW,KAAK,GAAGR,GAAG,CAACQ,KAAK;MACxBX,MAAM,CAACY,MAAM,GAAGT,GAAG,CAACS,MAAM;;MAE1B;MACAX,GAAG,CAACY,SAAS,CAACV,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;MAExB;MACA,MAAMW,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEf,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;MACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;MAE3B;MACA,MAAMC,IAAI,GAAG,IAAIC,iBAAiB,CAAClB,MAAM,CAACW,KAAK,GAAGX,MAAM,CAACY,MAAM,CAAC;MAChE,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACvCF,IAAI,CAACE,CAAC,GAAC,CAAC,CAAC,GAAIH,IAAI,CAACG,CAAC,CAAC,GAAG,KAAK,GAAGH,IAAI,CAACG,CAAC,GAAC,CAAC,CAAC,GAAG,KAAK,GAAGH,IAAI,CAACG,CAAC,GAAC,CAAC,CAAC,GAAG,KAAM;MACvE;;MAEA;MACA,MAAME,KAAK,GAAG,IAAIH,iBAAiB,CAAClB,MAAM,CAACW,KAAK,GAAGX,MAAM,CAACY,MAAM,CAAC;MACjE,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,MAAM,CAACY,MAAM,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;QAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,CAACW,KAAK,GAAG,CAAC,EAAEY,CAAC,EAAE,EAAE;UACzC,MAAMC,GAAG,GAAGF,CAAC,GAAGtB,MAAM,CAACW,KAAK,GAAGY,CAAC;UAChC,MAAME,EAAE,GACN,CAACR,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGxB,MAAM,CAACW,KAAK,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGxB,MAAM,CAACW,KAAK,CAAC,GAC5D,CAAC,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACO,GAAG,GAAG,CAAC,CAAC,GACtC,CAACP,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGxB,MAAM,CAACW,KAAK,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGxB,MAAM,CAACW,KAAK,CAAC;UAC9D,MAAMe,EAAE,GACN,CAACT,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,GAC3FM,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,CAAC,GAAGM,IAAI,CAACO,GAAG,GAAGxB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC;UAC5FU,KAAK,CAACG,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,IAAI,CAACJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,CAAC;QAC1D;MACF;;MAEA;MACA,MAAMI,UAAU,GAAG9B,MAAM,CAACW,KAAK,GAAG,CAAC;MACnC,IAAIoB,OAAO,GAAG,CAAC;MACf,IAAIC,UAAU,GAAG;QAAET,CAAC,EAAE,CAAC;QAAED,CAAC,EAAE,CAAC;QAAEX,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;;MAEpD;MACA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,MAAM,CAACY,MAAM,EAAEU,CAAC,EAAE,EAAE;QACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,CAACW,KAAK,EAAEY,CAAC,EAAE,EAAE;UACrC,IAAIF,KAAK,CAACC,CAAC,GAAGtB,MAAM,CAACW,KAAK,GAAGY,CAAC,CAAC,GAAG,GAAG,EAAE;YACrC,IAAIZ,KAAK,GAAG,CAAC;YACb,IAAIC,MAAM,GAAG,CAAC;YACd,IAAIqB,MAAM,GAAGV,CAAC;YACd,IAAIrD,MAAM,GAAGoD,CAAC;;YAEd;YACA,OAAOC,CAAC,GAAGvB,MAAM,CAACW,KAAK,IAAIU,KAAK,CAACC,CAAC,GAAGtB,MAAM,CAACW,KAAK,GAAGY,CAAC,CAAC,GAAG,GAAG,EAAE;cAC5DZ,KAAK,EAAE;cACPY,CAAC,EAAE;YACL;;YAEA;YACA,IAAIW,KAAK,GAAGZ,CAAC;YACb,OAAOY,KAAK,GAAGlC,MAAM,CAACY,MAAM,IAAIS,KAAK,CAACa,KAAK,GAAGlC,MAAM,CAACW,KAAK,GAAGsB,MAAM,CAAC,GAAG,GAAG,EAAE;cAC1ErB,MAAM,EAAE;cACRsB,KAAK,EAAE;YACT;;YAEA;YACA,MAAMC,OAAO,GAAGF,MAAM,GAAGtB,KAAK,GAAG,CAAC;YAClC,IAAIgB,IAAI,CAACS,GAAG,CAACD,OAAO,GAAGL,UAAU,CAAC,GAAG9B,MAAM,CAACW,KAAK,GAAG,GAAG,EAAE;cACvD,MAAM0B,IAAI,GAAG1B,KAAK,GAAGC,MAAM;cAC3B,IAAIyB,IAAI,GAAGN,OAAO,EAAE;gBAClBA,OAAO,GAAGM,IAAI;gBACdL,UAAU,GAAG;kBAAET,CAAC,EAAEU,MAAM;kBAAEX,CAAC,EAAEpD,MAAM;kBAAEyC,KAAK;kBAAEC;gBAAO,CAAC;cACtD;YACF;UACF;QACF;MACF;;MAEA;MACA,MAAM0B,WAAW,GAAGN,UAAU,CAACrB,KAAK;MACpC,MAAM4B,YAAY,GAAGvC,MAAM,CAACY,MAAM;;MAElC;MACA,MAAM4B,OAAO,GAAGF,WAAW,GAAGjD,iBAAiB;;MAE/C;MACA,MAAMoD,UAAU,GAAG1C,YAAY,GAAGV,iBAAiB;;MAEnD;MACA,MAAMqD,WAAW,GAAGf,IAAI,CAACgB,KAAK,CAAC3C,MAAM,CAACW,KAAK,GAAG8B,UAAU,CAAC;MACzD,MAAMG,YAAY,GAAGjB,IAAI,CAACgB,KAAK,CAAC3C,MAAM,CAACY,MAAM,GAAG6B,UAAU,CAAC;MAE3D,OAAO;QACLH,WAAW;QACXO,iBAAiB,EAAEN,YAAY;QAC/BE,UAAU,EAAEd,IAAI,CAACgB,KAAK,CAACF,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9CK,gBAAgB,EAAE,CAACJ,WAAW,EAAEE,YAAY,CAAC;QAC7CJ,OAAO,EAAEb,IAAI,CAACgB,KAAK,CAACH,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;QACxCO,YAAY,EAAE1D,iBAAiB;QAC/B2D,eAAe,EAAET,YAAY,GAAGC;MAClC,CAAC;IACH,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE;IAAK;IACZC,KAAK,EAAE,EAAE,CAAG;EACd,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC9B,MAAMC,uBAAuB,GAAG,EAAE,CAAC,CAAC;;EAEpC;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGT,mBAAmB,CAACnG,UAAU,CAAC;;IAExD;IACA,MAAM6G,iBAAiB,GAAGlC,IAAI,CAACmC,GAAG,CAAC5G,aAAa,GAAGoG,iBAAiB,EAAEC,uBAAuB,CAAC;;IAE9F;IACA,MAAMQ,qBAAqB,GAAGH,gBAAgB,GAAGC,iBAAiB;;IAElE;IACA,MAAMG,YAAY,GAAG1E,yBAAyB,GAAGuE,iBAAiB;;IAElE;IACA,MAAMI,aAAa,GAAGR,KAAK,CAACS,UAAU,GAAGF,YAAY;IACrD,MAAMG,cAAc,GAAGV,KAAK,CAACW,WAAW,GAAGJ,YAAY;IACvD,MAAMK,eAAe,GAAGZ,KAAK,CAACa,YAAY,GAAGN,YAAY;;IAEzD;IACA,MAAMO,iBAAiB,GAAIN,aAAa,GAAG1E,iBAAiB,GAAI,GAAG;IACnE,MAAMiF,kBAAkB,GAAIL,cAAc,GAAG3E,kBAAkB,GAAI,GAAG;IACtE,MAAMiF,mBAAmB,GAAIJ,eAAe,GAAG9E,iBAAiB,GAAI,GAAG;;IAEvE;IACA,MAAMmF,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLhE,KAAK,EAAEgB,IAAI,CAACmC,GAAG,CAACS,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvC3D,MAAM,EAAEe,IAAI,CAACmC,GAAG,CAACU,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1CF,YAAY,EAAEG,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTC,KAAK,EAAEjD,IAAI,CAACC,GAAG,CAAC2C,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGT,qBAAqB;MACxFc,SAAS,EAAEpB,KAAK,CAACS,UAAU;MAC3BY,UAAU,EAAErB,KAAK,CAACW,WAAW;MAC7BW,YAAY,EAAEtB,KAAK,CAACsB,YAAY;MAChCC,cAAc,EAAEjB,qBAAqB;MACrCF;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMoB,kBAAkB,GAAIC,MAAM,IAAK;IACrCjI,aAAa,CAACiI,MAAM,CAAC;IACrB/H,gBAAgB,CAAC+H,MAAM,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAChD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,SAAS,EAAEpJ,WAAW,KAAK;IACnD,MAAMqJ,cAAc,GAAG7B,wBAAwB,CAAC4B,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACX,SAAS;IACxC,IAAIa,SAAS,GAAGF,cAAc,CAACV,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAI3I,WAAW,EAAE;MACfsJ,SAAS,GAAGD,cAAc,CAACX,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,MAAMc,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMC,WAAW,GAAGL,SAAS,CAAChB,WAAW;IACzC,MAAMsB,gBAAgB,GAAGF,SAAS,GAAGC,WAAW;;IAEhD;IACA,QAAQL,SAAS,CAACO,IAAI;MACpB,KAAK,YAAY;QACf;QACAJ,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBX,SAAS,EAAEY,SAAS;MACpBX,SAAS,EAAEY,SAAS;MACpBX,KAAK,EAAEjD,IAAI,CAACC,GAAG,CAACyD,cAAc,CAACT,KAAK,EAAEc,gBAAgB,CAAC,CAAC;IAC1D,CAAC;EACH,CAAC;;EAED;EACA,MAAME,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,IAAI;IAAE;IACrB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,IAAI;IAAE;IACrB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,IAAI;IAAE;IACnBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,OAAO;IACbK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,GAAG;IAAE;IACpB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,QAAQ;IACdK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,IAAI;IAAE;IACrB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,CAAC;IAAE;IAClB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,YAAY;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,2BAA2B;IACjC;IACAf,YAAY,EAAE,EAAE;IAAE;IAClBgB,aAAa,EAAE,EAAE;IAAE;IACnB7B,UAAU,EAAE,EAAE;IAAE;IAChBE,WAAW,EAAE,EAAE;IAAE;IACjBE,YAAY,EAAE,EAAE;IAAE;IAClBqB,IAAI,EAAE,SAAS;IACfK,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAuC,CAAC,EACtE;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAuC,CAAC,EACrE;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAuC,CAAC,EACtE;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAuC,CAAC,EACxE;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAuC,CAAC,EACtE;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAuC,CAAC,CACzE;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB7F,KAAK,EAAE;YAAE8F,KAAK,EAAE;UAAK,CAAC;UACtB7F,MAAM,EAAE;YAAE6F,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAIhL,QAAQ,CAACiL,OAAO,EAAE;QACpBjL,QAAQ,CAACiL,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZ1D,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE2D,GAAG,CAAC;MAC7C;MACA,IAAIlL,gBAAgB,CAACgL,OAAO,EAAE;QAC5BhL,gBAAgB,CAACgL,OAAO,CAAChG,GAAG,GAAG,iBAAiB;QAChDhF,gBAAgB,CAACgL,OAAO,CAAC7H,KAAK,CAACgI,OAAO,GAAG,OAAO;MAClD;MACA3D,OAAO,CAAC4D,GAAG,CAAC,kCAAkC,CAAC;MAC/CjL,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM4K,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtL,QAAQ,CAACiL,OAAO,IAAI,CAAC/K,SAAS,CAAC+K,OAAO,EAAE,OAAO,IAAI;IAExD,MAAM1G,MAAM,GAAGrE,SAAS,CAAC+K,OAAO;IAChC,MAAMH,KAAK,GAAG9K,QAAQ,CAACiL,OAAO;IAC9B1G,MAAM,CAACW,KAAK,GAAG4F,KAAK,CAACS,UAAU;IAC/BhH,MAAM,CAACY,MAAM,GAAG2F,KAAK,CAACU,WAAW;IACjC,MAAMhH,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACY,SAAS,CAAC0F,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEvG,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;IACvD,OAAOZ,MAAM,CAACkH,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IAC9D,MAAMlH,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACmH,WAAW,GAAG,WAAW;IAE7BnH,GAAG,CAACK,MAAM,GAAG,YAAW;MACtB,MAAMR,MAAM,GAAGlF,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMgF,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnCF,MAAM,CAACW,KAAK,GAAGR,GAAG,CAACoH,YAAY;MAC/BvH,MAAM,CAACY,MAAM,GAAGT,GAAG,CAACqH,aAAa;MACjCvH,GAAG,CAACY,SAAS,CAACV,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAExB,IAAI;QACF,MAAMW,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEf,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;QACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;QAC3B,MAAML,KAAK,GAAGX,MAAM,CAACW,KAAK;QAC1B,MAAMC,MAAM,GAAGZ,MAAM,CAACY,MAAM;;QAE5B;QACA,MAAM6G,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC5B,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,GAAG,CAAC,EAAEY,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAGX,KAAK,GAAGY,CAAC,IAAI,CAAC;YAC/B,MAAMoG,CAAC,GAAG3G,IAAI,CAACQ,GAAG,CAAC;YACnB,MAAMoG,CAAC,GAAG5G,IAAI,CAACQ,GAAG,GAAG,CAAC,CAAC;YACvB,MAAMqG,CAAC,GAAG7G,IAAI,CAACQ,GAAG,GAAG,CAAC,CAAC;;YAEvB;YACA,IAAIsG,MAAM,GAAG,KAAK;YAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;gBAC/B,IAAIA,EAAE,KAAK,CAAC,IAAID,EAAE,KAAK,CAAC,EAAE;gBAC1B,MAAME,WAAW,GAAG,CAAC,CAAC3G,CAAC,GAAGyG,EAAE,IAAIpH,KAAK,IAAIY,CAAC,GAAGyG,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAME,EAAE,GAAGlH,IAAI,CAACiH,WAAW,CAAC;gBAC5B,MAAME,EAAE,GAAGnH,IAAI,CAACiH,WAAW,GAAG,CAAC,CAAC;gBAChC,MAAMG,EAAE,GAAGpH,IAAI,CAACiH,WAAW,GAAG,CAAC,CAAC;;gBAEhC;gBACA,MAAMI,SAAS,GAAG1G,IAAI,CAACS,GAAG,CAACuF,CAAC,GAAGO,EAAE,CAAC,GAAGvG,IAAI,CAACS,GAAG,CAACwF,CAAC,GAAGO,EAAE,CAAC,GAAGxG,IAAI,CAACS,GAAG,CAACyF,CAAC,GAAGO,EAAE,CAAC;gBACxE,IAAIC,SAAS,GAAG,EAAE,EAAE;kBAClBP,MAAM,GAAG,IAAI;kBACb;gBACF;cACF;cACA,IAAIA,MAAM,EAAE;YACd;YAEA,IAAIA,MAAM,EAAE;cACVL,UAAU,CAACa,GAAG,CAAC9G,GAAG,GAAG,CAAC,CAAC;YACzB;UACF;QACF;;QAEA;QACA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;UACvC,MAAMwG,CAAC,GAAG3G,IAAI,CAACG,CAAC,CAAC;UACjB,MAAMyG,CAAC,GAAG5G,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC;UACrB,MAAM0G,CAAC,GAAG7G,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC;UACrB,MAAMoH,UAAU,GAAGpH,CAAC,GAAG,CAAC;;UAExB;UACA,MAAMqH,UAAU,GAAG,CAACb,CAAC,GAAGC,CAAC,GAAGC,CAAC,IAAI,CAAC;;UAElC;UACA,MAAMY,UAAU,GAAGhB,UAAU,CAACiB,GAAG,CAACH,UAAU,CAAC;;UAE7C;UACA,MAAMI,WAAW,GACfhB,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPC,CAAC,GAAG,GAAG,IACPlG,IAAI,CAACS,GAAG,CAACuF,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpBjG,IAAI,CAACS,GAAG,CAACwF,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IACpB,CAACY,UACF;;UAED;UACA,IAAIE,WAAW,EAAE;YACf3H,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACnB,CAAC,MAAM,IAAIqH,UAAU,GAAG,GAAG,IAAI,CAACC,UAAU,EAAE;YAC1C;YACAzH,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC,GAAGQ,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE9C,IAAI,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAC7C;QACF;QAEAlB,GAAG,CAAC2I,YAAY,CAAC9H,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCsG,UAAU,CAAC1G,GAAG,GAAGV,MAAM,CAACkH,SAAS,CAAC,WAAW,CAAC;;QAE9C;QACAE,UAAU,CAACvI,KAAK,CAACgK,MAAM,GAAG,MAAM;QAChCzB,UAAU,CAACvI,KAAK,CAACiK,YAAY,GAAG,QAAQ;QACxC1B,UAAU,CAACvI,KAAK,CAACkK,OAAO,GAAG,GAAG;MAEhC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV9F,OAAO,CAAC+F,IAAI,CAAC,0BAA0B,EAAED,CAAC,CAAC;QAC3C;QACA5B,UAAU,CAACvI,KAAK,CAACgK,MAAM,GAAG,MAAM;QAChCzB,UAAU,CAACvI,KAAK,CAACiK,YAAY,GAAG,QAAQ;QACxC1B,UAAU,CAACvI,KAAK,CAACkK,OAAO,GAAG,GAAG;MAChC;IACF,CAAC;IAED5I,GAAG,CAACM,OAAO,GAAG,YAAW;MACvByC,OAAO,CAAC+F,IAAI,CAAC,sBAAsB,CAAC;MACpC;MACA7B,UAAU,CAACvI,KAAK,CAACgK,MAAM,GAAG,MAAM;MAChCzB,UAAU,CAACvI,KAAK,CAACiK,YAAY,GAAG,QAAQ;MACxC1B,UAAU,CAACvI,KAAK,CAACkK,OAAO,GAAG,GAAG;IAChC,CAAC;IAED5I,GAAG,CAACO,GAAG,GAAG0G,UAAU,CAAC1G,GAAG;EAC1B,CAAC;;EAED;EACA,MAAMwI,qBAAqB,GAAIpI,SAAS,IAAK;IAC3C;IACA,OAAOa,IAAI,CAACwH,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC3N,QAAQ,CAACiL,OAAO,IAAI,CAAC/K,SAAS,CAAC+K,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAG9K,QAAQ,CAACiL,OAAO;IAC9B,MAAM1G,MAAM,GAAGrE,SAAS,CAAC+K,OAAO;IAChC,MAAMzG,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAF,MAAM,CAACW,KAAK,GAAG4F,KAAK,CAACS,UAAU;IAC/BhH,MAAM,CAACY,MAAM,GAAG2F,KAAK,CAACU,WAAW;;IAEjC;IACAhH,GAAG,CAACY,SAAS,CAAC0F,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEvG,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;;IAEvD;IACA,MAAMyI,cAAc,GAAG9C,KAAK,CAAC+C,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAGlD,KAAK,CAACS,UAAU,GAAGT,KAAK,CAACU,WAAW;IACxD,MAAMyC,eAAe,GAAGH,aAAa,CAAC5I,KAAK,GAAG4I,aAAa,CAAC3I,MAAM;IAElE,IAAI+I,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAAC3I,MAAM;MACpC+I,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAAC5I,KAAK,IAAI,CAAC;MAClDmJ,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAAC5I,KAAK;MAClCiJ,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAAC3I,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMmJ,MAAM,GAAG/J,MAAM,CAACW,KAAK,GAAGgJ,YAAY;IAC1C,MAAMK,MAAM,GAAGhK,MAAM,CAACY,MAAM,GAAGgJ,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAGtI,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI6F,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAGvI,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI8F,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAGxI,IAAI,CAACC,GAAG,CAAC5B,MAAM,CAACW,KAAK,GAAGsJ,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAGzI,IAAI,CAACC,GAAG,CAAC5B,MAAM,CAACY,MAAM,GAAGsJ,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAG1I,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI6F,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAG3I,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAI8F,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAG5I,IAAI,CAACC,GAAG,CAAC5B,MAAM,CAACW,KAAK,GAAG0J,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAG7I,IAAI,CAACC,GAAG,CAAC5B,MAAM,CAACY,MAAM,GAAG0J,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGxK,GAAG,CAACc,YAAY,CAACkJ,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAACzJ,IAAI;;MAEnC;MACA,MAAM2J,eAAe,GAAG1K,GAAG,CAACc,YAAY,CAACsJ,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAC3J,IAAI;MAEvC,IAAI6J,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAACtD,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAMqD,UAAU,GAAGvD,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAIlG,IAAI,CAACS,GAAG,CAACuF,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAMuD,UAAU,GAAGxD,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIlG,IAAI,CAACS,GAAG,CAACuF,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAIjG,IAAI,CAACS,GAAG,CAACwF,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAMuD,UAAU,GAAGzD,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAMwD,UAAU,GAAG1D,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAOqD,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAIlK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,QAAQ,CAACtJ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAMwG,CAAC,GAAG+C,QAAQ,CAACvJ,CAAC,CAAC;QACrB,MAAMyG,CAAC,GAAG8C,QAAQ,CAACvJ,CAAC,GAAG,CAAC,CAAC;QACzB,MAAM0G,CAAC,GAAG6C,QAAQ,CAACvJ,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAI8J,UAAU,CAACtD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBgD,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAI3J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyJ,UAAU,CAACxJ,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAMwG,CAAC,GAAGiD,UAAU,CAACzJ,CAAC,CAAC;QACvB,MAAMyG,CAAC,GAAGgD,UAAU,CAACzJ,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM0G,CAAC,GAAG+C,UAAU,CAACzJ,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAI8J,UAAU,CAACtD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvBkD,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAOxI,KAAK,EAAE;MACdC,OAAO,CAAC+F,IAAI,CAAC,uBAAuB,EAAEhG,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMyI,2BAA2B,GAAGA,CAACC,WAAW,EAAEtE,WAAW,KAAK;IAChE;IACAtL,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMqJ,SAAS,GAAGQ,OAAO,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/F,IAAI,KAAK6F,WAAW,CAAC;;IAE3D;IACA3M,UAAU,CAAC,MAAM;MACfjD,kBAAkB,CAAC;QACjB+J,IAAI,EAAE6F,WAAW;QACjB3F,QAAQ,EAAE,CAAAZ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEY,QAAQ,KAAI,EAAE;QAAE;QACrC8F,UAAU,EAAE1G,SAAS,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAM2G,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACnQ,UAAU,EAAE;MACf,MAAMoQ,eAAe,GAAGjF,YAAY,CAAC,CAAC;MACtC,IAAIrL,gBAAgB,CAACgL,OAAO,IAAIsF,eAAe,EAAE;QAC/CtQ,gBAAgB,CAACgL,OAAO,CAAChG,GAAG,GAAGsL,eAAe;QAC9CtQ,gBAAgB,CAACgL,OAAO,CAAC7H,KAAK,CAACgI,OAAO,GAAG,OAAO;MAClD;MACAhL,aAAa,CAAC,IAAI,CAAC;MACnBkB,oBAAoB,CAAC,KAAK,CAAC;MAC3BZ,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC/BI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACA,IAAIZ,SAAS,CAAC+K,OAAO,IAAIjL,QAAQ,CAACiL,OAAO,EAAE;QACzC,MAAM1G,MAAM,GAAGrE,SAAS,CAAC+K,OAAO;QAChC,MAAMzG,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACnCF,MAAM,CAACW,KAAK,GAAGlF,QAAQ,CAACiL,OAAO,CAACM,UAAU;QAC1ChH,MAAM,CAACY,MAAM,GAAGnF,QAAQ,CAACiL,OAAO,CAACO,WAAW;QAC5ChH,GAAG,CAACY,SAAS,CAACpF,QAAQ,CAACiL,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM5F,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEf,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;QACrE3E,cAAc,CAACiN,qBAAqB,CAACpI,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACL3E,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM8P,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIvQ,gBAAgB,CAACgL,OAAO,EAAE;MAC5BhL,gBAAgB,CAACgL,OAAO,CAAC7H,KAAK,CAACgI,OAAO,GAAG,MAAM;IACjD;IACAhL,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BkB,qBAAqB,CAAC,KAAK,CAAC;IAC5Bd,gBAAgB,CAAC,IAAI,CAAC;IACtBQ,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMmP,qBAAqB,GAAIC,IAAI,IAAK;IACtChP,gBAAgB,CAACgP,IAAI,CAAC;EACxB,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnChQ,YAAY,CAACgQ,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,OAAO,IAAK;IACvCb,2BAA2B,CAACa,OAAO,CAACzG,IAAI,EAAE1J,SAAS,CAAC;EACtD,CAAC;;EAED;EACA,MAAMoQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC5Q,UAAU,EAAE;MACf;MACA,MAAM6Q,cAAc,GAAG,qBAAqB,CAAC,CAAC;;MAE9C,IAAI/Q,gBAAgB,CAACgL,OAAO,EAAE;QAC5BhL,gBAAgB,CAACgL,OAAO,CAAChG,GAAG,GAAG+L,cAAc;QAC7C/Q,gBAAgB,CAACgL,OAAO,CAAC7H,KAAK,CAACgI,OAAO,GAAG,OAAO;;QAEhD;QACAnL,gBAAgB,CAACgL,OAAO,CAAClG,MAAM,GAAG,MAAM;UACtC;UACAkM,uBAAuB,CAAC,CAAC;QAC3B,CAAC;MACH;MAEA7Q,aAAa,CAAC,IAAI,CAAC;MACnBkB,oBAAoB,CAAC,IAAI,CAAC;MAC1BZ,uBAAuB,CAAC,IAAI,CAAC;MAC7BI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAN,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMyQ,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAChR,gBAAgB,CAACgL,OAAO,EAAE;IAE/B,IAAI;MACF;MACA,MAAM1G,MAAM,GAAGlF,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMgF,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMC,GAAG,GAAGzE,gBAAgB,CAACgL,OAAO;MAEpC1G,MAAM,CAACW,KAAK,GAAGR,GAAG,CAACoH,YAAY,IAAI,GAAG;MACtCvH,MAAM,CAACY,MAAM,GAAGT,GAAG,CAACqH,aAAa,IAAI,GAAG;MACxCvH,GAAG,CAACY,SAAS,CAACV,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEH,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;;MAErD;MACA;MACA,MAAM+L,YAAY,GAAGC,yBAAyB,CAAC5M,MAAM,CAAC;MAEtD,IAAI2M,YAAY,EAAE;QAChBzJ,OAAO,CAAC4D,GAAG,CAAC,wCAAwC,EAAE6F,YAAY,CAAC;QACnE;QACApO,MAAM,CAACsO,iBAAiB,GAAGF,YAAY;MACzC,CAAC,MAAM;QACLzJ,OAAO,CAAC+F,IAAI,CAAC,+CAA+C,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOhG,KAAK,EAAE;MACdC,OAAO,CAAC+F,IAAI,CAAC,mCAAmC,EAAEhG,KAAK,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAM2J,yBAAyB,GAAI5M,MAAM,IAAK;IAC5C,IAAI;MACF,MAAMC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,MAAMY,SAAS,GAAGb,GAAG,CAACc,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEf,MAAM,CAACW,KAAK,EAAEX,MAAM,CAACY,MAAM,CAAC;MACrE,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;MAE3B;MACA;;MAEA;MACA,IAAI8L,MAAM,GAAG,IAAI;MACjB,IAAIC,MAAM,GAAG/M,MAAM,CAACW,KAAK,GAAG,GAAG,CAAC,CAAC;MACjC,IAAIqM,QAAQ,GAAGhN,MAAM,CAACW,KAAK;;MAE3B;MACA,KAAK,IAAIW,CAAC,GAAGK,IAAI,CAACsL,KAAK,CAACjN,MAAM,CAACY,MAAM,GAAG,GAAG,CAAC,EAAEU,CAAC,GAAGtB,MAAM,CAACY,MAAM,GAAG,GAAG,EAAEU,CAAC,IAAI,CAAC,EAAE;QAC7E,IAAI4L,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;;QAElB;QACA,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,CAACW,KAAK,EAAEY,CAAC,EAAE,EAAE;UACrC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAGtB,MAAM,CAACW,KAAK,GAAGY,CAAC,IAAI,CAAC;UACtC,MAAMoG,CAAC,GAAG3G,IAAI,CAACQ,GAAG,CAAC;UACnB,MAAMoG,CAAC,GAAG5G,IAAI,CAACQ,GAAG,GAAG,CAAC,CAAC;UACvB,MAAMqG,CAAC,GAAG7G,IAAI,CAACQ,GAAG,GAAG,CAAC,CAAC;UACvB,MAAM4L,KAAK,GAAGpM,IAAI,CAACQ,GAAG,GAAG,CAAC,CAAC;;UAE3B;UACA,IAAI4L,KAAK,GAAG,GAAG,KAAKzF,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,CAAC,EAAE;YAChD,IAAIqF,QAAQ,KAAK,CAAC,CAAC,EAAEA,QAAQ,GAAG3L,CAAC;YACjC4L,SAAS,GAAG5L,CAAC;UACf;QACF;QAEA,IAAI2L,QAAQ,KAAK,CAAC,CAAC,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;UACvC,MAAMxM,KAAK,GAAGwM,SAAS,GAAGD,QAAQ;UAClC,IAAIvM,KAAK,GAAGqM,QAAQ,IAAIrM,KAAK,GAAG,EAAE,EAAE;YAAE;YACpCqM,QAAQ,GAAGrM,KAAK;YAChBmM,MAAM,GAAGxL,CAAC;YACVyL,MAAM,GAAG,CAACG,QAAQ,GAAGC,SAAS,IAAI,CAAC;UACrC;QACF;MACF;MAEA,IAAIL,MAAM,KAAK,IAAI,EAAE;QACnB;QACA,OAAO;UACLvL,CAAC,EAAEwL,MAAM,GAAG,EAAE;UACdzL,CAAC,EAAEwL,MAAM,GAAG,EAAE;UACdnM,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,EAAE;UACVuB,OAAO,EAAE4K,MAAM;UACfM,OAAO,EAAEP,MAAM,GAAG,EAAE;UAAE;UACtBQ,UAAU,EAAE,GAAG;UACfC,WAAW,EAAE;QACf,CAAC;MACH;MAEA,OAAO,IAAI;IAEb,CAAC,CAAC,OAAOtK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtE;MACA,OAAO;QACL1B,CAAC,EAAEvB,MAAM,CAACW,KAAK,GAAG,GAAG,GAAG,EAAE;QAC1BW,CAAC,EAAEtB,MAAM,CAACY,MAAM,GAAG,IAAI,GAAG,EAAE;QAC5BD,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,EAAE;QACVuB,OAAO,EAAEnC,MAAM,CAACW,KAAK,GAAG,GAAG;QAC3B0M,OAAO,EAAErN,MAAM,CAACY,MAAM,GAAG,IAAI,GAAG,EAAE;QAClC0M,UAAU,EAAE,GAAG;QACfC,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC3Q,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd4L,UAAU,CAAC,CAAC;IACZ;IACArJ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgD,oBAAoB,IAAI1B,UAAU,EAAE;IAEzC,MAAM6R,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,MAAMjC,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CzL,mBAAmB,CAAC8N,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAAC7N,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAAC4N,cAAc,IAAI7N,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMkQ,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACnQ,oBAAoB,EAAE1B,UAAU,EAAEgC,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,iBAAiB,IAAIhC,UAAU,EAAE;MACpC6B,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAMmQ,iBAAiB,GAAGF,WAAW,CAAC,MAAM;MAC1CjQ,YAAY,CAACoQ,IAAI,IAAI;QACnB;QACA,IAAI,CAACnQ,gBAAgB,EAAE;UACrBiQ,aAAa,CAACC,iBAAiB,CAAC;UAChC/P,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAIgQ,IAAI,IAAI,CAAC,EAAE;UACb;UACAF,aAAa,CAACC,iBAAiB,CAAC;UAChC/P,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChCwO,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAO8B,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMF,aAAa,CAACC,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAChQ,iBAAiB,EAAEhC,UAAU,EAAE8B,gBAAgB,CAAC,CAAC;;EAErD;EACA,MAAMoQ,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAACzQ,oBAAoB;IACtCC,uBAAuB,CAACwQ,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACblQ,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMqQ,mBAAmB,GAAGA,CAAA,KAAM;IAChCzQ,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BF,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBJ,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7B;IACAxB,YAAY,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAM0S,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO7R,SAAS,KAAK,SAAS,GAAGwJ,OAAO,GAAGK,SAAS;EACtD,CAAC;;EAED;EACA,MAAMiI,gBAAgB,GAAIlF,CAAC,IAAK;IAC9B/K,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAAC6K,CAAC,CAACmF,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC;EACjC,CAAC;EAED,MAAMC,eAAe,GAAIrF,CAAC,IAAK;IAC7B,IAAI,CAAChL,UAAU,EAAE;IAEjB,MAAMsQ,QAAQ,GAAGtF,CAAC,CAACmF,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACrC,MAAMG,IAAI,GAAGD,QAAQ,GAAGpQ,MAAM;;IAE9B;IACA,IAAIqQ,IAAI,GAAG,CAAC,EAAE;MACZxQ,gBAAgB,CAACwQ,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACxQ,UAAU,EAAE;IAEjBC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvB3B,uBAAuB,CAAC,KAAK,CAAC;IAChC;;IAEA;IACA4B,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACd,MAAMmU,kBAAkB,GAAIzF,CAAC,IAAK;MAChC,IAAI5L,kBAAkB,IAAI,CAAC4L,CAAC,CAAC0F,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7DtR,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDvC,QAAQ,CAACiE,gBAAgB,CAAC,WAAW,EAAE0P,kBAAkB,CAAC;IAC1D3T,QAAQ,CAACiE,gBAAgB,CAAC,YAAY,EAAE0P,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACX3T,QAAQ,CAACmE,mBAAmB,CAAC,WAAW,EAAEwP,kBAAkB,CAAC;MAC7D3T,QAAQ,CAACmE,mBAAmB,CAAC,YAAY,EAAEwP,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACrR,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMwR,aAAa,GAAGA,CAAA,kBACpBlU,OAAA;IAAKmE,KAAK,EAAEgQ,MAAM,CAACC,gBAAiB;IAAAC,QAAA,eAClCrU,OAAA;MAAKmE,KAAK,EAAEgQ,MAAM,CAACG,WAAY;MAAAD,QAAA,gBAC7BrU,OAAA;QAAImE,KAAK,EAAEgQ,MAAM,CAACI,OAAQ;QAAAF,QAAA,EAAC;MAAsB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtD3U,OAAA;QAAGmE,KAAK,EAAEgQ,MAAM,CAACS,UAAW;QAAAP,QAAA,EAAC;MAA6E;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9G3U,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACU,SAAU;QAAAR,QAAA,eAC3BrU,OAAA,CAACF,SAAS;UACRgV,KAAK,EAAC,yCAAyC;UAC/CrD,IAAI,EAAE,GAAI;UACVsD,KAAK,EAAC,GAAG;UACTC,aAAa,EAAE;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3U,OAAA;QAAGmE,KAAK,EAAEgQ,MAAM,CAACc,MAAO;QAAAZ,QAAA,EAAC;MAAuC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpE3U,OAAA;QACEmE,KAAK,EAAEgQ,MAAM,CAACe,OAAQ;QACtBC,OAAO,EAAEtU,YAAa;QACtB,cAAW,MAAM;QAAAwT,QAAA,EAClB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAI3S,SAAS,EAAE;IACb,oBAAOhC,OAAA,CAACkU,aAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,oBACE3U,OAAA;IAAKmE,KAAK,EAAEgQ,MAAM,CAACiB,SAAU;IAAAf,QAAA,GAE1BnS,mBAAmB,iBAClBlC,OAAA;MAAKmE,KAAK,EAAEgQ,MAAM,CAACkB,iBAAkB;MAAAhB,QAAA,eACnCrU,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACmB,eAAgB;QAAAjB,QAAA,eACjCrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACoB,iBAAkB;UAAAlB,QAAA,gBACnCrU,OAAA;YAAImE,KAAK,EAAEgQ,MAAM,CAACqB,eAAgB;YAAAnB,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD3U,OAAA;YAAKmE,KAAK,EAAEgQ,MAAM,CAACsB,gBAAiB;YAAApB,QAAA,gBAClCrU,OAAA;cAAGmE,KAAK,EAAEgQ,MAAM,CAACuB,eAAgB;cAAArB,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3U,OAAA;cAAGmE,KAAK,EAAEgQ,MAAM,CAACuB,eAAgB;cAAArB,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3U,OAAA;YACEmE,KAAK,EAAEgQ,MAAM,CAACwB,gBAAiB;YAC/BR,OAAO,EAAErC,qBAAsB;YAAAuB,QAAA,EAChC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3U,OAAA;MACE4V,IAAI,EAAC,GAAG;MACRzR,KAAK,EAAEgQ,MAAM,CAAC0B,YAAa;MAC3B,cAAW,cAAc;MAAAxB,QAAA,eAEzBrU,OAAA;QAAKiG,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAAC4P,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,OAAO;QAAA1B,QAAA,eAC1DrU,OAAA;UAAMgW,CAAC,EAAC;QAA8D;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGJ3U,OAAA;MAAKmE,KAAK,EAAEgQ,MAAM,CAAC8B,wBAAyB;MAAA5B,QAAA,gBAC1CrU,OAAA;QACEgG,GAAG,EAAC,qBAAqB;QACzBkQ,GAAG,EAAC,eAAe;QACnB/R,KAAK,EAAE;UACL,GAAGgQ,MAAM,CAACgC,gBAAgB;UAC1BhI,MAAM,EAAE;QACV;MAAE;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF3U,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACiC,gBAAiB;QAAA/B,QAAA,gBAClCrU,OAAA;UAAMmE,KAAK,EAAE;YACX,GAAGgQ,MAAM,CAACkC,iBAAiB;YAC3BC,KAAK,EAAElU,iBAAiB,GAAG,SAAS,GAAG+R,MAAM,CAACkC,iBAAiB,CAACC;UAClE,CAAE;UAAAjC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpB3U,OAAA;UAAMmE,KAAK,EAAE;YACX,GAAGgQ,MAAM,CAACoC,gBAAgB;YAC1BD,KAAK,EAAElU,iBAAiB,GAAG,SAAS,GAAG+R,MAAM,CAACoC,gBAAgB,CAACD;UACjE,CAAE;UAAAjC,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3U,OAAA;MAAKmE,KAAK,EAAEgQ,MAAM,CAACqC,eAAgB;MAAAnC,QAAA,gBACjCrU,OAAA;QACEyW,GAAG,EAAE1V,QAAS;QACdoD,KAAK,EAAEgQ,MAAM,CAACuC,UAAW;QACzBC,QAAQ;QACRC,WAAW;QACXC,KAAK;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACF3U,OAAA;QAAQyW,GAAG,EAAExV,SAAU;QAACkD,KAAK,EAAE;UAAEgI,OAAO,EAAE;QAAO;MAAE;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD3U,OAAA;QACEyW,GAAG,EAAEzV,gBAAiB;QACtBmD,KAAK,EAAEgQ,MAAM,CAAC2C,aAAc;QAC5BZ,GAAG,EAAC;MAAe;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAACzT,UAAU,iBACVlB,OAAA;QAAKmE,KAAK,EAAE;UACV4S,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAElV,QAAQ,GAAG,EAAE,GAAG,EAAE;UACvBmV,KAAK,EAAEnV,QAAQ,GAAG,EAAE,GAAG,EAAE;UACzBoV,MAAM,EAAE,EAAE;UACV/K,OAAO,EAAE,MAAM;UACfgL,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,oBAAoB;UACrCC,YAAY,EAAE,MAAM;UACpBC,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE,YAAY;UAClCC,SAAS,EAAE,+BAA+B;UAC1CC,MAAM,EAAE;QACV,CAAE;QAAAvD,QAAA,gBACArU,OAAA;UAAO6X,SAAS,EAAC,cAAc;UAAAxD,QAAA,EAAC;QAEhC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR3U,OAAA;UAAO6X,SAAS,EAAC,kBAAkB;UAAAxD,QAAA,gBACjCrU,OAAA;YACEiL,IAAI,EAAC,UAAU;YACf6M,OAAO,EAAElV,oBAAqB;YAC9BmV,QAAQ,EAAE3E,uBAAwB;YAClC4E,QAAQ,EAAE9U,iBAAkB;YAC5B,cAAW;UAAqB;YAAAsR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF3U,OAAA;YAAM6X,SAAS,EAAC;UAAe;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAKAzR,iBAAiB,iBAChBlD,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAAC8D,gBAAiB;QAAA5D,QAAA,gBAClCrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAAC+D,eAAgB;UAAA7D,QAAA,EAAEvR;QAAS;UAAA0R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD3U,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACgE,aAAc;UAAA9D,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGA,CAACzT,UAAU,IAAI,CAACgC,iBAAiB,IAAI,CAACN,oBAAoB,iBACzD5C,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACiE,oBAAqB;QAAA/D,QAAA,eACtCrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACkE,eAAgB;UAAAhE,QAAA,EAAC;QAEpC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/R,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9DlD,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACmE,kBAAmB;QAAAjE,QAAA,gBACpCrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACoE,eAAgB;UAAAlE,QAAA,EAAC;QAA6C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvF3U,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACqE,kBAAmB;UAAAnE,QAAA,EAAC;QAAgD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CACN,EAEA/R,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7DlD,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAACsE,aAAc;QAAApE,QAAA,eAC/BrU,OAAA;UAAKmE,KAAK,EAAE;YAAC,GAAGgQ,MAAM,CAACuE,UAAU;YAAEnB,eAAe,EAAE;UAAyB,CAAE;UAAAlD,QAAA,EAAC;QAEhF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/S,aAAa,iBACZ5B,OAAA;QACEmE,KAAK,EAAE;UACL,GAAGgQ,MAAM,CAACwE,SAAS;UACnBtK,OAAO,EAAEzL,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7DmL,MAAM,EAAEvL,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACF6U,SAAS,EAAE/V,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAAuS,QAAA,eAElBrU,OAAA;UAAK8V,OAAO,EAAC,aAAa;UAAC8C,KAAK,EAAC,4BAA4B;UAAAvE,QAAA,gBAE3DrU,OAAA;YACEgW,CAAC,EAAC,4EAA4E;YAC9E6C,MAAM,EACJjW,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD8V,WAAW,EAAC,GAAG;YACf/C,IAAI,EAAC,MAAM;YACXgD,aAAa,EAAC;UAAO;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACF3U,OAAA;YACEgW,CAAC,EAAC,4EAA4E;YAC9E6C,MAAM,EACJjW,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD8V,WAAW,EAAC,GAAG;YACf/C,IAAI,EAAC,MAAM;YACXgD,aAAa,EAAC;UAAO;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEF3U,OAAA;YACE6G,CAAC,EAAC,KAAK;YACPD,CAAC,EAAC,KAAK;YACPX,KAAK,EAAC,KAAK;YACXC,MAAM,EAAC,KAAK;YACZ6P,IAAI,EACFnT,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDqL,OAAO,EAAEzL,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpEgW,EAAE,EAAC,IAAI;YACPH,MAAM,EACJjW,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD8V,WAAW,EAAC;UAAG;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEF3U,OAAA;YACEiZ,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRjM,CAAC,EAAC,IAAI;YACN8I,IAAI,EACFnT,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDqL,OAAO,EAAEzL,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClE6V,MAAM,EACJjW,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACD8V,WAAW,EAAC;UAAG;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAED/R,oBAAoB,iBACnB5C,OAAA,CAAAE,SAAA;YAAAmU,QAAA,gBACErU,OAAA;cAAM6G,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACuS,UAAU,EAAC,QAAQ;cAACpD,IAAI,EAAC,OAAO;cAACqD,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAhF,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3U,OAAA;cAAM6G,CAAC,EAAC,KAAK;cAACD,CAAC,EAAC,KAAK;cAACuS,UAAU,EAAC,QAAQ;cAACpD,IAAI,EAAC,OAAO;cAACqD,QAAQ,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAhF,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvT,eAAe,IAAIF,UAAU,iBAC5BlB,OAAA;QAAKmE,KAAK,EAAE;UACV,GAAGgQ,MAAM,CAACmF,eAAe;UACzB;UACA;UACAtC,GAAG,EAAE5U,iBAAiB,GAAG,KAAK,GAAG,KAAK;UACtCmX,SAAS,EAAEnX,iBAAiB,GAAG,kCAAkC,GAAG,uBAAuB;UAC3F6D,KAAK,EAAEvE,SAAS,KAAK,SAAS,GAAG,GAAGqD,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;UACzEkB,MAAM,EAAExE,SAAS,KAAK,SAAS,GAC3B,CAAC,MAAM;YACL,MAAMwH,gBAAgB,GAAG1E,kBAAkB;YAC3C,MAAMgV,YAAY,GAAIhX,aAAa,IAAIgC,kBAAmB;YAE1D,IAAIgV,YAAY,EAAE;cAChB;cACA,MAAMC,YAAY,GAAG,CAACjX,aAAa,GAAGgC,kBAAkB,IAAIA,kBAAkB;cAC9E,OAAO,GAAGS,YAAY,IAAI,CAAC,GAAGwU,YAAY,GAAG,GAAG,CAAC,GAAG;YACtD;YACA,OAAO,GAAGxU,YAAY,GAAG;UAC3B,CAAC,EAAE,CAAC,GACJ,GAAGC,eAAe,GAAG;UACzB;UACAwU,QAAQ,EAAE,CAAC,MAAM;YACf,MAAMF,YAAY,GAAIhX,aAAa,IAAIgC,kBAAmB;YAC1D,OAAO9C,SAAS,KAAK,SAAS,IAAI8X,YAAY,GAC1C,gCAAgC,GAChC9X,SAAS,KAAK,SAAS,IAAIc,aAAa,GAAGgC,kBAAkB,GAC3D,gCAAgC,GAChC,MAAM;UACd,CAAC,EAAE,CAAC;UACJmV,QAAQ,EAAE;QACZ,CAAE;QAAAtF,QAAA,eACArU,OAAA;UAAKmE,KAAK,EAAE;YACV4S,QAAQ,EAAE,UAAU;YACpB9Q,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiG,OAAO,EAAE,MAAM;YACfiL,UAAU,EAAE,QAAQ;YACpBwC,cAAc,EAAE;UAClB,CAAE;UAAAvF,QAAA,gBACArU,OAAA;YACEgG,GAAG,EAAE,OAAO5E,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAACgK,IAAI,GAAGhK,eAAgB;YAClF8U,GAAG,EAAC,kBAAkB;YACtB/R,KAAK,EAAE;cACL8B,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACd2T,SAAS,EAAE,SAAS;cACpBN,SAAS,EAAE7X,SAAS,KAAK,WAAW,GAChC,CAAC,MAAM;gBACL;gBACA,MAAMoY,aAAa,GAAG,uBAAuB5U,eAAe,GAAG,EAAE,GAAG;;gBAEpE;gBACA;gBACA,IAAI9C,iBAAiB,EAAE;kBACrB;kBACA,OAAO,GAAG0X,aAAa,YAAY,CAAC,CAAC;gBACvC,CAAC,MAAM,IAAIxY,WAAW,EAAE;kBACtB;kBACA,OAAO,GAAGwY,aAAa,aAAa;gBACtC,CAAC,MAAM;kBACL;kBACA,OAAO,GAAGA,aAAa,YAAY;gBACrC;cACF,CAAC,EAAE,CAAC,GACJ,CAACC,qBAAA,IAAM;gBACL,MAAM7Q,gBAAgB,GAAGT,mBAAmB,CAACnG,UAAU,CAAC;gBACxD,MAAM6G,iBAAiB,GAAGlC,IAAI,CAACmC,GAAG,CAAC5G,aAAa,GAAGoG,iBAAiB,EAAEC,uBAAuB,CAAC;gBAC9F,MAAM2Q,YAAY,GAAIlX,UAAU,KAAK,KAAK,IAAI6G,iBAAiB,IAAI,EAAE,IAChD7G,UAAU,KAAK,OAAO,IAAI6G,iBAAiB,IAAI,EAAG;gBAEvE,IAAIqQ,YAAY,EAAE;kBAChB;kBACA,MAAMC,YAAY,GAAG,CAACtQ,iBAAiB,GAAGD,gBAAgB,IAAIA,gBAAgB;kBAC9E,MAAM8Q,WAAW,GAAG,CAAC,GAAIP,YAAY,GAAG,GAAI,CAAC,CAAC;kBAC9C,MAAMQ,UAAU,GAAG/Q,gBAAgB,GAAGC,iBAAiB,CAAC,CAAC;;kBAEzD,OAAO,SAAUlE,YAAY,GAAG,EAAE,GAAIgV,UAAU,YAAYA,UAAU,YAAYD,WAAW,GAAG;gBAClG;;gBAEA;gBACA,OAAO,SAAS/S,IAAI,CAACC,GAAG,CACrBjC,YAAY,GAAG,EAAE,IAAKkE,iBAAiB,GAAGD,gBAAgB,GACvDA,gBAAgB,GAAGC,iBAAiB,GACpCD,gBAAgB,GAAGC,iBAAiB,CAAC,EACzC,GAAG,IAAI,EAAA4Q,qBAAA,GAAA3Y,eAAe,CAACgQ,UAAU,cAAA2I,qBAAA,uBAA1BA,qBAAA,CAA4BrQ,WAAW,KAAI,EAAE,CAAC,CAAC;gBACxD,CAAC,YAAYP,iBAAiB,GAAGD,gBAAgB,GAC7CA,gBAAgB,GAAGC,iBAAiB,GACpC,CAAC,GAAG;cACV,CAAC,EAAE,CAAC;cACRgF,MAAM,EAAE;YACV,CAAE;YACF+L,MAAM,EAAG5L,CAAC,IAAK7B,gBAAgB,CAAC6B,CAAC,CAAC0F,MAAM,EAAEtS,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,UAAU;UAAE;YAAA8S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,EACDjT,SAAS,KAAK,SAAS,IAAI,OAAON,eAAe,KAAK,QAAQ,iBAC7DpB,OAAA;YAAKmE,KAAK,EAAE;cACV4S,QAAQ,EAAE,UAAU;cACpBoD,MAAM,EAAE,OAAO;cACfC,IAAI,EAAE,KAAK;cACXb,SAAS,EAAE,kBAAkB;cAC7BH,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjB/C,KAAK,EAAE,OAAO;cACdiB,eAAe,EAAE,yBAAyB;cAC1CD,OAAO,EAAE,SAAS;cAClBE,YAAY,EAAE,MAAM;cACpB6C,UAAU,EAAE,QAAQ;cACpBC,aAAa,EAAE,MAAM;cACrB3C,SAAS,EAAE,2BAA2B;cACtCT,MAAM,EAAE;YACV,CAAE;YAAA7C,QAAA,GACCjT,eAAe,CAACkK,QAAQ,EAAC,IAC1B,EAAC9I,aAAa,KAAKgC,kBAAkB,iBACnCxE,OAAA;cAAMmE,KAAK,EAAE;gBACXiV,QAAQ,EAAE,MAAM;gBAChB/K,OAAO,EAAE,GAAG;gBACZkM,UAAU,EAAE;cACd,CAAE;cAAAlG,QAAA,EACC,CAAC,MAAM;gBACN,MAAM/J,cAAc,GAAG9F,kBAAkB,GAAGhC,aAAa;gBAEzD,IAAIgY,iBAAiB;gBACrB,IAAIhY,aAAa,GAAGgC,kBAAkB,EAAE;kBACtC;kBACA,MAAMiW,cAAc,GAAGjW,kBAAkB,GAAGhC,aAAa;kBACzD,MAAMkY,iBAAiB,GAAGlW,kBAAkB,GAAG,IAAI;kBACnD,MAAMmW,iBAAiB,GAAG1T,IAAI,CAACC,GAAG,CAACuT,cAAc,EAAEC,iBAAiB,CAAC;kBACrE,MAAME,mBAAmB,GAAG,CAAC,GAAID,iBAAiB,GAAGnW,kBAAkB,GAAI,GAAG;kBAC9EgW,iBAAiB,GAAG,CAAC,CAACI,mBAAmB,GAAG,CAAC,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAClE,CAAC,MAAM;kBACL;kBACAL,iBAAiB,GAAG,CAAC,CAAClQ,cAAc,GAAG,CAAC,IAAI,GAAG,EAAEuQ,OAAO,CAAC,CAAC,CAAC;gBAC7D;;gBAEA;gBACA,MAAMC,WAAW,GAAGtY,aAAa,IAAIgC,kBAAkB,GAAG,KAAK,GAAG,EAAE;gBACpE,OAAO,IAAIhC,aAAa,GAAGgC,kBAAkB,GAAG,GAAG,GAAG,EAAE,GAAGgW,iBAAiB,KAAKM,WAAW,EAAE;cAChG,CAAC,EAAE;YAAC;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACzT,UAAU,iBACVlB,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAAC4G,cAAe;QAAA1G,QAAA,gBAEhCrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAAC6G,oBAAqB;UAAA3G,QAAA,gBACtCrU,OAAA;YACEmE,KAAK,EAAEgQ,MAAM,CAAC8G,UAAW;YACzBpD,SAAS,EAAE/V,QAAQ,GAAG,oBAAoB,GAAG,EAAG;YAChDqT,OAAO,EAAE9D,aAAc;YACvB,cAAW,SAAS;YAAAgD,QAAA,eAEpBrU,OAAA;cAAKmE,KAAK,EAAEgQ,MAAM,CAAC+G,YAAa;cAACrD,SAAS,EAAE/V,QAAQ,GAAG,qBAAqB,GAAG;YAAG;cAAA0S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eACT3U,OAAA;YAAMmE,KAAK,EAAEgQ,MAAM,CAACgH,WAAY;YAAA9G,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAGN3U,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACiH,kBAAmB;UAAA/G,QAAA,gBACpCrU,OAAA;YACEmE,KAAK,EAAEgQ,MAAM,CAACkH,QAAS;YACvBxD,SAAS,EAAE/V,QAAQ,GAAG,kBAAkB,GAAG,EAAG;YAC9CqT,OAAO,EAAErD,gBAAiB;YAC1B,cAAW,cAAc;YAAAuC,QAAA,eAEzBrU,OAAA;cAAKiG,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAAC4P,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,OAAO;cAAA1B,QAAA,eAC1DrU,OAAA;gBAAMgW,CAAC,EAAC;cAA0R;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT3U,OAAA;YAAMmE,KAAK,EAAEgQ,MAAM,CAACgH,WAAY;YAAA9G,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAzT,UAAU,iBACTlB,OAAA;QACEmE,KAAK,EAAEgQ,MAAM,CAACmH,eAAgB;QAC9BnG,OAAO,EAAEA,CAAA,KAAM1T,uBAAuB,CAAC,IAAI,CAAE;QAC7C,cAAW,iBAAiB;QAAA4S,QAAA,eAE5BrU,OAAA;UAAKiG,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAAC4P,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,OAAO;UAAA1B,QAAA,eAC1DrU,OAAA;YAAMgW,CAAC,EAAC;UAA6D;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGE,CAAC,EAGLzT,UAAU,iBACTlB,OAAA;MACEmE,KAAK,EAAEgQ,MAAM,CAACoH,oBAAqB;MACnC1D,SAAS,EAAE/V,QAAQ,GAAG,YAAY,GAAG,EAAG;MACxCqT,OAAO,EAAEA,CAAA,KAAMxS,qBAAqB,CAAC,IAAI,CAAE;MAC3C,cAAW,mBAAmB;MAAA0R,QAAA,gBAE9BrU,OAAA;QAAKiG,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAAC4P,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,OAAO;QAAA1B,QAAA,eAC1DrU,OAAA;UAAMgW,CAAC,EAAC;QAA64B;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACp5B,CAAC,eACN3U,OAAA;QAAMmE,KAAK,EAAEgQ,MAAM,CAACqH,aAAc;QAAAnH,QAAA,GAAE7R,aAAa,EAAC,IAAE;MAAA;QAAAgS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACT,EAGAjS,kBAAkB,iBACjB1C,OAAA;MACEmE,KAAK,EAAEgQ,MAAM,CAACsH,YAAa;MAC3BtG,OAAO,EAAEA,CAAA,KAAMxS,qBAAqB,CAAC,KAAK,CAAE;MAC5CkV,SAAS,EAAC,eAAe;MAAAxD,QAAA,eAEzBrU,OAAA;QACEmE,KAAK,EAAEgQ,MAAM,CAACuH,cAAe;QAC7BvG,OAAO,EAAG7G,CAAC,IAAKA,CAAC,CAACqN,eAAe,CAAC,CAAE;QACpC9D,SAAS,EAAC,eAAe;QAAAxD,QAAA,gBAEzBrU,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAACyH,WAAY;UAAAvH,QAAA,gBAC7BrU,OAAA;YAAImE,KAAK,EAAEgQ,MAAM,CAAC0H,UAAW;YAAAxH,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpD3U,OAAA;YACEmE,KAAK,EAAEgQ,MAAM,CAAC2H,aAAc;YAC5B3G,OAAO,EAAEA,CAAA,KAAMxS,qBAAqB,CAAC,KAAK,CAAE;YAC5C,cAAW,OAAO;YAAA0R,QAAA,eAElBrU,OAAA;cAAKiG,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAAC4P,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAA1B,QAAA,eACjErU,OAAA;gBAAMgW,CAAC,EAAC;cAAuG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3U,OAAA;UAAKmE,KAAK,EAAEgQ,MAAM,CAAC4H,YAAa;UAAA1H,QAAA,eAE9BrU,OAAA;YAAKmE,KAAK,EAAEgQ,MAAM,CAAC6H,eAAgB;YAAA3H,QAAA,gBACjCrU,OAAA;cAAOmE,KAAK,EAAEgQ,MAAM,CAAC8H,WAAY;cAAA5H,QAAA,GAAC,cACpB,EAAC7R,aAAa,EAAC,IAC7B;YAAA;cAAAgS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3U,OAAA;cACEiL,IAAI,EAAC,OAAO;cACZ/D,GAAG,EAAE5E,UAAU,KAAK,KAAK,GAAG,IAAI,GAAG,IAAK;cACxC8G,GAAG,EAAE9G,UAAU,KAAK,KAAK,GAAG,IAAI,GAAG,IAAK;cACxCwS,KAAK,EAAEtS,aAAc;cACrBuV,QAAQ,EAAGzJ,CAAC,IAAKkD,qBAAqB,CAAC0K,QAAQ,CAAC5N,CAAC,CAAC0F,MAAM,CAACc,KAAK,CAAC,CAAE;cACjE3Q,KAAK,EAAEgQ,MAAM,CAACgI;YAAO;cAAA3H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACF3U,OAAA;cAAKmE,KAAK,EAAEgQ,MAAM,CAACiI,YAAa;cAAA/H,QAAA,gBAC9BrU,OAAA;gBAAAqU,QAAA,EAAO/R,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG;cAAM;gBAAAkS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD3U,OAAA;gBAAAqU,QAAA,EAAO/R,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG;cAAM;gBAAAkS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAGN3U,OAAA;cAAKmE,KAAK,EAAEgQ,MAAM,CAACkI,aAAc;cAAAhI,QAAA,gBAC/BrU,OAAA;gBACEmE,KAAK,EAAEgQ,MAAM,CAACmI,YAAa;gBAC3BnH,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAAChN,kBAAkB,CAAE;gBAAA6P,QAAA,GAC1D,WACU,EAAC7P,kBAAkB,EAAC,KAC/B;cAAA;gBAAAgQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3U,OAAA;gBACEmE,KAAK,EAAEgQ,MAAM,CAACmI,YAAa;gBAC3BnH,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAAC/M,cAAc,CAAE;gBAAA4P,QAAA,GACtD,SACQ,EAAC5P,cAAc,EAAC,KACzB;cAAA;gBAAA+P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3U,OAAA;gBACEmE,KAAK,EAAEgQ,MAAM,CAACmI,YAAa;gBAC3BnH,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAAC9M,cAAc,CAAE;gBAAA2P,QAAA,GACtD,SACQ,EAAC3P,cAAc,EAAC,KACzB;cAAA;gBAAA8P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnT,oBAAoB,iBACnBxB,OAAA;MACEyW,GAAG,EAAE/S,QAAS;MACdS,KAAK,EAAE;QACL,GAAGgQ,MAAM,CAACoI,gBAAgB;QAC1BhD,SAAS,EAAE,cAAcnW,aAAa,KAAK;QAC3CoZ,WAAW,EAAE;MACf,CAAE;MACF3E,SAAS,EAAE/V,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjB2a,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAElJ,gBAAiB;MAC/BmJ,WAAW,EAAEhJ,eAAgB;MAC7BiJ,UAAU,EAAE9I,cAAe;MAAAO,QAAA,gBAE3BrU,OAAA;QACEmE,KAAK,EAAEgQ,MAAM,CAAC0I,UAAW;QACzB,eAAY,MAAM;QAClBH,YAAY,EAAElJ,gBAAiB;QAC/BmJ,WAAW,EAAEhJ,eAAgB;QAC7BiJ,UAAU,EAAE9I;MAAe;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACF3U,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAAC2I,WAAY;QAAAzI,QAAA,gBAC7BrU,OAAA;UACEmE,KAAK,EAAE;YACL,GAAGgQ,MAAM,CAAC4I,GAAG;YACb,IAAIrb,SAAS,KAAK,SAAS,GAAGyS,MAAM,CAACzS,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACFyT,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,SAAS,CAAE;UAAA2C,QAAA,EAC3C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3U,OAAA;UACEmE,KAAK,EAAE;YACL,GAAGgQ,MAAM,CAAC4I,GAAG;YACb,IAAIrb,SAAS,KAAK,WAAW,GAAGyS,MAAM,CAACzS,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACFyT,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,WAAW,CAAE;UAAA2C,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3U,OAAA;QAAKmE,KAAK,EAAEgQ,MAAM,CAAC6I,aAAc;QAACnF,SAAS,EAAC,gBAAgB;QAAAxD,QAAA,EACzDd,kBAAkB,CAAC,CAAC,CAAC0J,GAAG,CAAC,CAACpL,OAAO,EAAEqL,KAAK,KAAK;UAC5C;UACA,IAAI,CAACrL,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMsL,UAAU,GAAG,CAAC,OAAO/b,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgK,IAAI,GAAGhK,eAAe,MAAMyQ,OAAO,CAACzG,IAAI;UAEnH,oBACEpL,OAAA;YAEEmE,KAAK,EAAE;cACL,GAAGgQ,MAAM,CAACiJ,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/C5F,eAAe,EAAE4F,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFG,KAAK,EAAE,GAAGzL,OAAO,CAAC1G,IAAI,MAAM0G,OAAO,CAACxH,YAAY,IAAI,KAAK,IAAK;YAC9D8K,OAAO,EAAEA,CAAA,KAAMvD,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAC1G,IAAI,IAAI0G,OAAO,CAACxH,YAAY,IAAI,KAAK,IAAK;YAAAgK,QAAA,gBAExErU,OAAA;cACEgG,GAAG,EAAE6L,OAAO,CAACzG,IAAK;cAClB8K,GAAG,EAAErE,OAAO,CAAC1G,IAAK;cAClBhH,KAAK,EAAEgQ,MAAM,CAACoJ,YAAa;cAC3BC,OAAO,EAAGlP,CAAC,IAAK;gBACdA,CAAC,CAAC0F,MAAM,CAACpF,aAAa,CAACzK,KAAK,CAACgI,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3U,OAAA;cAAKmE,KAAK,EAAEgQ,MAAM,CAACsJ,YAAa;cAAApJ,QAAA,gBAC9BrU,OAAA;gBAAKmE,KAAK,EAAEgQ,MAAM,CAACuJ,WAAY;gBAAArJ,QAAA,EAAExC,OAAO,CAAC1G;cAAI;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnDjT,SAAS,KAAK,SAAS,IAAImQ,OAAO,CAACxH,YAAY,iBAC9CrK,OAAA;gBAAKmE,KAAK,EAAEgQ,MAAM,CAACwJ,WAAY;gBAAAtJ,QAAA,GAAExC,OAAO,CAACxH,YAAY,EAAC,IAAE;cAAA;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBDuI,KAAK;YAAA1I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA7T,EAAA,CA5qDMF,YAAY;AAAAgd,EAAA,GAAZhd,YAAY;AA6qDlB,MAAMuT,MAAM,GAAG;EACbiB,SAAS,EAAE;IACT2B,QAAQ,EAAE,UAAU;IACpB7Q,MAAM,EAAE,4BAA4B;IACpCiG,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBI,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,MAAM;IACbuH,UAAU,EAAE,4EAA4E;IACxFlE,QAAQ,EAAE,QAAQ;IAClB6C,WAAW,EAAE,cAAc;IAC3BsB,uBAAuB,EAAE,aAAa;IACtCC,uBAAuB,EAAE,OAAO,CAAC;EACnC,CAAC;EACDvH,eAAe,EAAE;IACfwH,IAAI,EAAE,CAAC;IACPjH,QAAQ,EAAE,UAAU;IACpB4C,QAAQ,EAAE,QAAQ;IAClBpC,eAAe,EAAE,MAAM;IACvBpL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE;EAClB,CAAC;EACDlD,UAAU,EAAE;IACVzQ,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,OAAO;IAClBN,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACDzC,aAAa,EAAE;IACbC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNoD,IAAI,EAAE,CAAC;IACPnU,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,OAAO;IAClB1N,OAAO,EAAE,MAAM;IACf8R,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAED/I,OAAO,EAAE;IACP6B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZ7C,eAAe,EAAE,oBAAoB;IACrCjB,KAAK,EAAE,OAAO;IACdgB,OAAO,EAAE,MAAM;IACfE,YAAY,EAAE,KAAK;IACnB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBhH,MAAM,EAAE,EAAE;IACVU,MAAM,EAAE,MAAM;IACdD,SAAS,EAAE,+BAA+B;IAC1CwG,UAAU,EAAE,eAAe;IAC3BhS,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB3T,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdkY,OAAO,EAAE;EACX,CAAC;EACDC,OAAO,EAAE;IACPtH,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZ7C,eAAe,EAAE,oBAAoB;IACrCjB,KAAK,EAAE,OAAO;IACdgB,OAAO,EAAE,MAAM;IACfE,YAAY,EAAE,KAAK;IACnB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBhH,MAAM,EAAE,EAAE;IACVU,MAAM,EAAE,MAAM;IACdD,SAAS,EAAE,+BAA+B;IAC1CwG,UAAU,EAAE,eAAe;IAC3BhS,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB3T,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdkY,OAAO,EAAE;EACX,CAAC;EACDE,eAAe,EAAE;IACfvH,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,EAAE;IACV/K,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE,MAAM;IACfC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,MAAM;IACpBC,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCC,SAAS,EAAE,+BAA+B;IAC1CC,MAAM,EAAE;EACV,CAAC;EACD2G,WAAW,EAAE;IACXxH,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZnU,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,MAAM;IACpBI,MAAM,EAAE,oCAAoC;IAC5CV,MAAM,EAAE,CAAC;IACTiH,UAAU,EAAE;EACd,CAAC;EACDK,YAAY,EAAE;IACZzH,QAAQ,EAAE,UAAU;IACpB9Q,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdsR,YAAY,EAAE,KAAK;IACnBI,MAAM,EAAE,MAAM;IACdsG,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BhS,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB1C,MAAM,EAAE,EAAE;IACVuH,MAAM,EAAE,KAAK;IACb9G,SAAS,EAAE,8BAA8B;IACzCyG,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACT7E,SAAS,EAAE;IACb;EACF,CAAC;EACDmF,WAAW,EAAE;IACXtF,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChBtH,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE,MAAM;IACpBD,eAAe,EAAE,oBAAoB;IACrCsH,aAAa,EAAE;EACjB,CAAC;EACD5G,gBAAgB,EAAE;IAChBlB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCrC,MAAM,EAAE,EAAE;IACV4H,SAAS,EAAE,QAAQ;IACnBxE,aAAa,EAAE,MAAM;IACrBhD,OAAO,EAAE,WAAW;IACpBC,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,MAAM;IACpBC,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCC,SAAS,EAAE;EACb,CAAC;EACDO,eAAe,EAAE;IACfkB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChBqI,UAAU,EAAE,8BAA8B;IAC1CI,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE;EACb,CAAC;EACD7G,aAAa,EAAE;IACbiB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE;EACd,CAAC;EACDlG,aAAa,EAAE;IACb1B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCrC,MAAM,EAAE,EAAE;IACV4H,SAAS,EAAE,QAAQ;IACnBxE,aAAa,EAAE,MAAM;IACrBhD,OAAO,EAAE,WAAW;IACpBE,YAAY,EAAE,MAAM;IACpBD,eAAe,EAAE,oBAAoB;IACrCE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCC,SAAS,EAAE;EACb,CAAC;EACDe,UAAU,EAAE;IACVU,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CpH,eAAe,EAAE,0BAA0B;IAC3CD,OAAO,EAAE,WAAW;IACpBE,YAAY,EAAE,MAAM;IACpBuH,YAAY,EAAE,KAAK;IACnBZ,UAAU,EAAE;EACd,CAAC;EACDc,aAAa,EAAE;IACb7F,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CpH,eAAe,EAAE,oBAAoB;IACrCD,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE;EAChB,CAAC;EACDmB,SAAS,EAAE;IACT5B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCtT,KAAK,EAAE,KAAK;IACZiZ,QAAQ,EAAE,OAAO;IACjBhZ,MAAM,EAAE,MAAM;IACdmI,OAAO,EAAE,GAAG;IACZiM,aAAa,EAAE,MAAM;IACrBpD,MAAM,EAAE,CAAC;IACT/I,MAAM,EAAE,iDAAiD;IACzDgR,YAAY,EAAE,iDAAiD;IAC/DhB,UAAU,EAAE;EACd,CAAC;EACH7E,eAAe,EAAE;IACfvC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IACVoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCrC,MAAM,EAAE,CAAC;IACT/K,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB3T,KAAK,EAAE,MAAM;IAAE;IACfmZ,WAAW,EAAE,SAAS;IAAE;IACxB9M,QAAQ,EAAE,OAAO;IACjB+M,SAAS,EAAE,OAAO;IAAE;IACpB/E,aAAa,EAAE;EACjB,CAAC;EAECW,UAAU,EAAE;IACVhV,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,0BAA0B;IAC3CC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE,+BAA+B;IAC1CF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EACDwD,YAAY,EAAE;IACZjV,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,KAAK;IACnB2G,UAAU,EAAE;EACd,CAAC;EACDmB,QAAQ,EAAE;IACRvI,QAAQ,EAAE,UAAU;IACpBoD,MAAM,EAAE,MAAM;IACdlD,KAAK,EAAE,MAAM;IACbhR,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBhH,MAAM,EAAE,EAAE;IACViH,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdD,SAAS,EAAE,+BAA+B;IAC1CyG,OAAO,EAAE,MAAM;IACf9G,OAAO,EAAE;EACX,CAAC;EAED;EACAiE,oBAAoB,EAAE;IACpBxE,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbM,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,OAAO;IACdgB,OAAO,EAAE,WAAW;IACpBE,YAAY,EAAE,MAAM;IACpB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBtG,MAAM,EAAE,MAAM;IACdzL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,KAAK;IACV+G,OAAO,EAAE,MAAM;IACfD,UAAU,EAAE,eAAe;IAC3BjH,MAAM,EAAE,EAAE;IACVS,SAAS,EAAE,mCAAmC;IAC9C0H,SAAS,EAAE,MAAM;IACjB/M,QAAQ,EAAE,MAAM;IAChBwL,uBAAuB,EAAE,aAAa;IACtCtB,WAAW,EAAE;EACf,CAAC;EACDhB,aAAa,EAAE;IACbpC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE;EACd,CAAC;EAED;EACAoC,YAAY,EAAE;IACZ1E,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNoD,IAAI,EAAE,CAAC;IACPnD,KAAK,EAAE,CAAC;IACRkD,MAAM,EAAE,CAAC;IACT5C,eAAe,EAAE,oBAAoB;IACrCpL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB1C,MAAM,EAAE,EAAE;IACVI,OAAO,EAAE,MAAM;IACfG,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjC8E,WAAW,EAAE;EACf,CAAC;EACDd,cAAc,EAAE;IACdnE,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,MAAM;IACpBvR,KAAK,EAAE,MAAM;IACbiZ,QAAQ,EAAE,MAAM;IAChBK,SAAS,EAAE,MAAM;IACjB5F,QAAQ,EAAE,QAAQ;IAClBhC,SAAS,EAAE,gCAAgC;IAC3CxL,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBsH,MAAM,EAAE,MAAM;IACd1H,QAAQ,EAAE;EACZ,CAAC;EACD6E,WAAW,EAAE;IACXzP,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,eAAe;IAC/BtC,OAAO,EAAE,qBAAqB;IAC9BkI,YAAY,EAAE;EAChB,CAAC;EACD3D,UAAU,EAAE;IACVzC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChBmI,MAAM,EAAE;EACV,CAAC;EACD3C,aAAa,EAAE;IACb7V,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdsR,YAAY,EAAE,KAAK;IACnBI,MAAM,EAAE,MAAM;IACdL,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,MAAM;IACb4H,MAAM,EAAE,SAAS;IACjB/R,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBuE,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE;EACX,CAAC;EACDrC,YAAY,EAAE;IACZzE,OAAO,EAAE,qBAAqB;IAC9BnL,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBE,GAAG,EAAE,MAAM;IACXoI,SAAS,EAAE,MAAM;IACjB1B,uBAAuB,EAAE,OAAO;IAChCwB,SAAS,EAAE;EACb,CAAC;EAED;EACAG,gBAAgB,EAAE;IAChBvT,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE;EACP,CAAC;EACDsI,cAAc,EAAE;IACdvG,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChBmI,MAAM,EAAE,CAAC;IACTK,SAAS,EAAE;EACb,CAAC;EACDc,iBAAiB,EAAE;IACjBxG,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,MAAM;IACbmI,MAAM,EAAE,CAAC;IACTK,SAAS,EAAE,QAAQ;IACnBe,UAAU,EAAE;EACd,CAAC;EACDC,eAAe,EAAE;IACf3T,OAAO,EAAE,MAAM;IACfkL,GAAG,EAAE,MAAM;IACXpR,KAAK,EAAE,MAAM;IACbiZ,QAAQ,EAAE;EACZ,CAAC;EACDa,YAAY,EAAE;IACZ/B,IAAI,EAAE,CAAC;IACP1G,OAAO,EAAE,WAAW;IACpBE,YAAY,EAAE,MAAM;IACpB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,mBAAmB;IAC3BL,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,MAAM;IACb8H,OAAO,EAAE;EACX,CAAC;EACD4B,kBAAkB,EAAE;IAClBzI,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,SAAS;IAChB+G,WAAW,EAAE,SAAS;IACtB1F,SAAS,EAAE;EACb,CAAC;EACDqE,eAAe,EAAE;IACf/V,KAAK,EAAE,MAAM;IACbiZ,QAAQ,EAAE,OAAO;IACjB/S,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBE,GAAG,EAAE;EACP,CAAC;EACD4E,WAAW,EAAE;IACX7C,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,MAAM;IACbwI,SAAS,EAAE,QAAQ;IACnB3S,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBvC,GAAG,EAAE;EACP,CAAC;EACD4I,UAAU,EAAE;IACV7G,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChBiB,eAAe,EAAE,yBAAyB;IAC1CD,OAAO,EAAE,SAAS;IAClBE,YAAY,EAAE;EAChB,CAAC;EACD2E,MAAM,EAAE;IACNlW,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,KAAK;IACbsR,YAAY,EAAE,KAAK;IACnB0I,UAAU,EAAE,SAAS;IACrB9B,OAAO,EAAE,MAAM;IACfF,MAAM,EAAE,SAAS;IACjBiC,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACDhE,YAAY,EAAE;IACZjQ,OAAO,EAAE,MAAM;IACfyN,cAAc,EAAE,eAAe;IAC/BR,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,MAAM;IACbsI,SAAS,EAAE;EACb,CAAC;EACDvC,aAAa,EAAE;IACblQ,OAAO,EAAE,MAAM;IACfkL,GAAG,EAAE,KAAK;IACVpR,KAAK,EAAE,MAAM;IACbiZ,QAAQ,EAAE;EACZ,CAAC;EACD5C,YAAY,EAAE;IACZ0B,IAAI,EAAE,CAAC;IACP1G,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE,KAAK;IACnB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,mBAAmB;IAC3BL,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,MAAM;IACb8H,OAAO,EAAE;EACX,CAAC;EACDiC,cAAc,EAAE;IACdpa,KAAK,EAAE,MAAM;IACbiZ,QAAQ,EAAE,OAAO;IACjB5H,OAAO,EAAE,WAAW;IACpBC,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,SAAS;IAChBkB,YAAY,EAAE,MAAM;IACpB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE;EACb,CAAC;EAED4E,gBAAgB,EAAE;IAChBxF,QAAQ,EAAE,UAAU;IACpBoD,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPnD,KAAK,EAAE,CAAC;IACRM,eAAe,EAAE,2BAA2B;IAC5CE,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC4I,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5BjJ,OAAO,EAAE,MAAM;IACfiI,SAAS,EAAE,MAAM;IACjBpT,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBD,MAAM,EAAE,EAAE;IACVS,SAAS,EAAE,iCAAiC;IAC5CC,MAAM,EAAE,MAAM;IACd2B,SAAS,EAAE,eAAe;IAC1B4E,UAAU,EAAE,yBAAyB;IACrCxE,QAAQ,EAAE,QAAQ;IAClB6C,WAAW,EAAE,MAAM;IACnBgE,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,QAAQ,EAAE;IACR5J,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,MAAM;IACbX,KAAK,EAAE,MAAM;IACb4H,MAAM,EAAE,SAAS;IACjBhH,MAAM,EAAE,EAAE;IACVjR,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdiG,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBpC,YAAY,EAAE,KAAK;IACnBD,eAAe,EAAE,oBAAoB;IACrC4G,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACf9G,OAAO,EAAE;EACX,CAAC;EACDwF,WAAW,EAAE;IACX3Q,OAAO,EAAE,MAAM;IACf4S,YAAY,EAAE,MAAM;IACpBxH,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBF,OAAO,EAAE,KAAK;IACdD,GAAG,EAAE;EACP,CAAC;EACD0F,GAAG,EAAE;IACHiB,IAAI,EAAE,CAAC;IACPc,SAAS,EAAE,QAAQ;IACnBxH,OAAO,EAAE,WAAW;IACpBE,YAAY,EAAE,KAAK;IACnB4B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3B7H,KAAK,EAAE,MAAM;IACb8H,OAAO,EAAE,MAAM;IACfxG,MAAM,EAAE,MAAM;IACdL,eAAe,EAAE,aAAa;IAC9BuG,uBAAuB,EAAE,aAAa;IACtCtB,WAAW,EAAE;EACf,CAAC;EACD9a,SAAS,EAAE;IACT6V,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,SAAS;IAChBqB,SAAS,EAAE;EACb,CAAC;EACDqF,aAAa,EAAE;IACb7Q,OAAO,EAAE,MAAM;IACfyU,mBAAmB,EAAE,uCAAuC;IAC5DvJ,GAAG,EAAE,MAAM;IACXkI,SAAS,EAAE,oBAAoB;IAC/BE,SAAS,EAAE,MAAM;IACjBoB,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,cAAc;IAC9BhD,uBAAuB,EAAE;EAC3B,CAAC;EACDX,WAAW,EAAE;IACXrG,QAAQ,EAAE,UAAU;IACpB9Q,KAAK,EAAE,MAAM;IACbmZ,WAAW,EAAE,KAAK;IAClB7H,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,MAAM;IACpBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,mBAAmB;IAC3B+B,QAAQ,EAAE,QAAQ;IAClBhC,SAAS,EAAE,+BAA+B;IAC1CL,OAAO,EAAE,KAAK;IACd8G,OAAO,EAAE,MAAM;IACfN,uBAAuB,EAAE,aAAa;IACtCtB,WAAW,EAAE;EACf,CAAC;EACDe,YAAY,EAAE;IACZtX,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,SAAS;IACpBrC,YAAY,EAAE,KAAK;IACnBD,eAAe,EAAE;EACnB,CAAC;EACDkG,YAAY,EAAE;IACZ1G,QAAQ,EAAE,UAAU;IACpBoD,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACXnD,KAAK,EAAE,KAAK;IACZmC,QAAQ,EAAE,KAAK;IACf9C,KAAK,EAAE,MAAM;IACbwI,SAAS,EAAE,QAAQ;IACnBvH,eAAe,EAAE,2BAA2B;IAC5CC,YAAY,EAAE,KAAK;IACnBF,OAAO,EAAE,SAAS;IAClBqC,QAAQ,EAAE;EACZ,CAAC;EACD+D,WAAW,EAAE;IACXtE,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBgB,UAAU,EAAE,QAAQ;IACpB2G,YAAY,EAAE,UAAU;IACxBrH,QAAQ,EAAE,QAAQ;IAClBoF,YAAY,EAAE;EAChB,CAAC;EACDpB,WAAW,EAAE;IACXvE,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChB+D,UAAU,EAAE;EACd,CAAC;EACDwC,UAAU,EAAE;IACV5W,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,KAAK;IACbqR,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,KAAK;IACnBiH,MAAM,EAAE,aAAa;IACrBP,MAAM,EAAE,MAAM;IACd1B,WAAW,EAAE,MAAM;IACnBiE,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAEDtM,gBAAgB,EAAE;IAChB2C,QAAQ,EAAE,UAAU;IACpB7Q,MAAM,EAAE,OAAO;IACfiG,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBrC,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE;EACX,CAAC;EACDhD,WAAW,EAAE;IACXiD,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,MAAM;IACfE,YAAY,EAAE,MAAM;IACpBG,SAAS,EAAE,gCAAgC;IAC3CmH,SAAS,EAAE,QAAQ;IACnBI,QAAQ,EAAE,OAAO;IACjBjZ,KAAK,EAAE;EACT,CAAC;EACDsO,OAAO,EAAE;IACP6E,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChByI,YAAY,EAAE;EAChB,CAAC;EACDnK,UAAU,EAAE;IACVwE,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,MAAM;IACbyI,YAAY,EAAE,MAAM;IACpBc,UAAU,EAAE;EACd,CAAC;EACDhL,SAAS,EAAE;IACT0C,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,MAAM;IACfE,YAAY,EAAE,MAAM;IACpBrL,OAAO,EAAE,cAAc;IACvB4S,YAAY,EAAE,MAAM;IACpBpH,SAAS,EAAE;EACb,CAAC;EACD1C,MAAM,EAAE;IACNmE,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,SAAS;IAChByI,YAAY,EAAE,MAAM;IACpBkC,SAAS,EAAE;EACb,CAAC;EAED;EACA5L,iBAAiB,EAAE;IACjB0B,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNoD,IAAI,EAAE,CAAC;IACPnD,KAAK,EAAE,CAAC;IACRkD,MAAM,EAAE,CAAC;IACT5C,eAAe,EAAE,oBAAoB;IACrCpL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB1C,MAAM,EAAE,IAAI;IACZO,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE;EACxB,CAAC;EACDpC,eAAe,EAAE;IACfiC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,MAAM;IACpBF,OAAO,EAAE,GAAG;IACZ4H,QAAQ,EAAE,OAAO;IACjBjZ,KAAK,EAAE,KAAK;IACZ0R,SAAS,EAAE,gCAAgC;IAC3CC,MAAM,EAAE;EACV,CAAC;EACDrC,iBAAiB,EAAE;IACjB+B,OAAO,EAAE,WAAW;IACpBwH,SAAS,EAAE;EACb,CAAC;EACDtJ,eAAe,EAAE;IACf4D,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,SAAS;IAChByI,YAAY,EAAE,MAAM;IACpBN,MAAM,EAAE;EACV,CAAC;EACDhJ,gBAAgB,EAAE;IAChBsJ,YAAY,EAAE;EAChB,CAAC;EACDrJ,eAAe,EAAE;IACf0D,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,MAAM;IACbuJ,UAAU,EAAE,KAAK;IACjBd,YAAY,EAAE,MAAM;IACpBN,MAAM,EAAE,YAAY;IACpBK,SAAS,EAAE;EACb,CAAC;EACDnJ,gBAAgB,EAAE;IAChB4B,eAAe,EAAE,SAAS;IAC1BjB,KAAK,EAAE,OAAO;IACdsB,MAAM,EAAE,MAAM;IACdJ,YAAY,EAAE,MAAM;IACpBF,OAAO,EAAE,WAAW;IACpB8B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB6E,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BxG,SAAS,EAAE;EACb,CAAC;EAED;EACAS,oBAAoB,EAAE;IACpBrB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IAAE;IACZoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCrC,MAAM,EAAE,EAAE;IACVoD,aAAa,EAAE;EACjB,CAAC;EACDjC,eAAe,EAAE;IACfe,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdwI,SAAS,EAAE,QAAQ;IACnBxH,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE,MAAM;IACpBD,eAAe,EAAE,oBAAoB;IACrCE,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjCC,SAAS,EAAE,8BAA8B;IACzCC,MAAM,EAAE,oCAAoC;IAC5C+G,UAAU,EAAE,8BAA8B;IAC1CkB,UAAU,EAAE,KAAK;IACjBX,QAAQ,EAAE;EACZ,CAAC;EAED;EACA5G,kBAAkB,EAAE;IAClBvB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,KAAK;IAAE;IACZoD,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,uBAAuB;IAClCrC,MAAM,EAAE,EAAE;IACV4H,SAAS,EAAE,QAAQ;IACnBxE,aAAa,EAAE,MAAM;IACrBhD,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE,MAAM;IACpBD,eAAe,EAAE,oBAAoB;IACrCE,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjCC,SAAS,EAAE;EACb,CAAC;EACDY,eAAe,EAAE;IACfa,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CpH,eAAe,EAAE,0BAA0B;IAC3CD,OAAO,EAAE,UAAU;IACnBE,YAAY,EAAE,MAAM;IACpBuH,YAAY,EAAE,KAAK;IACnBZ,UAAU,EAAE;EACd,CAAC;EACD3F,kBAAkB,EAAE;IAClBY,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CpH,eAAe,EAAE,oBAAoB;IACrCD,OAAO,EAAE,SAAS;IAClBE,YAAY,EAAE;EAChB,CAAC;EAED;EACA0J,0BAA0B,EAAE;IAC1BnK,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZjO,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,MAAM;IACXH,MAAM,EAAE,EAAE;IACVK,eAAe,EAAE,iBAAiB;IAAE;IACpCD,OAAO,EAAE,GAAG;IACZE,YAAY,EAAE,GAAG;IACjBG,SAAS,EAAE;EACb,CAAC;EACDwJ,qBAAqB,EAAE;IACrBlb,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,SAAS;IACpB1N,OAAO,EAAE,cAAc;IACvBiV,aAAa,EAAE;EACjB,CAAC;EACDC,mBAAmB,EAAE;IACnBlV,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,YAAY;IACxByI,UAAU,EAAE;EACd,CAAC;EACDyB,aAAa,EAAE;IACblI,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfhL,OAAO,EAAE,IAAI;IACbwQ,aAAa,EAAE,QAAQ;IACvBE,YAAY,EAAE;EAChB,CAAC;EACDwC,YAAY,EAAE;IACZnI,QAAQ,EAAE,MAAM;IAChB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfwF,aAAa,EAAE,QAAQ;IACvBD,SAAS,EAAE;EACb,CAAC;EAED;EACA7D,cAAc,EAAE;IACdhE,QAAQ,EAAE,UAAU;IACpBoD,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXb,SAAS,EAAE,kBAAkB;IAC7BpN,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,MAAM;IACXH,MAAM,EAAE;EACV,CAAC;EACD8D,oBAAoB,EAAE;IACpB7O,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE;EACP,CAAC;EACD+D,kBAAkB,EAAE;IAClBjP,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE;EACP,CAAC;EACD8D,WAAW,EAAE;IACX/B,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjB/C,KAAK,EAAE,OAAO;IACdqI,UAAU,EAAE,8BAA8B;IAC1CpH,eAAe,EAAE,oBAAoB;IACrCD,OAAO,EAAE,SAAS;IAClBE,YAAY,EAAE,KAAK;IACnB8C,aAAa,EAAE;EACjB,CAAC;EACDe,QAAQ,EAAE;IACRpV,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,yBAAyB;IAC1CC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE,oCAAoC;IAC/CF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EAED;EACA4D,eAAe,EAAE;IACfvE,QAAQ,EAAE,UAAU;IACpBoD,MAAM,EAAE,MAAM;IACdlD,KAAK,EAAE,MAAM;IACbhR,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,yBAAyB;IAC1CC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE,oCAAoC;IAC/CF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCR,MAAM,EAAE;EACV,CAAC;EAED;EACArB,YAAY,EAAE;IACZkB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZnU,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxB4H,cAAc,EAAE,MAAM;IACtBtD,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE,+BAA+B;IAC1CF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCR,MAAM,EAAE;EACV,CAAC;EAED;EACAgK,0BAA0B,EAAE;IAC1BnK,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IAAE;IACdjO,OAAO,EAAE,MAAM;IACfkL,GAAG,EAAE,MAAM;IACXH,MAAM,EAAE,EAAE;IACVK,eAAe,EAAE,iBAAiB;IAAE;IACpCD,OAAO,EAAE,GAAG;IACZE,YAAY,EAAE,GAAG;IACjBG,SAAS,EAAE;EACb,CAAC;EACDwJ,qBAAqB,EAAE;IACrBlb,KAAK,EAAE,MAAM;IAAE;IACfC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,SAAS;IACpB1N,OAAO,EAAE,cAAc;IACvBiV,aAAa,EAAE;EACjB,CAAC;EACDC,mBAAmB,EAAE;IACnBlV,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,YAAY;IACxByI,UAAU,EAAE,GAAG,CAAE;EACnB,CAAC;EACDyB,aAAa,EAAE;IACblI,QAAQ,EAAE,MAAM;IAAE;IAClB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfhL,OAAO,EAAE,IAAI;IACbwQ,aAAa,EAAE,QAAQ;IACvBE,YAAY,EAAE,KAAK,CAAE;EACvB,CAAC;EACDwC,YAAY,EAAE;IACZnI,QAAQ,EAAE,MAAM;IAAE;IAClB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfwF,aAAa,EAAE,QAAQ;IACvBD,SAAS,EAAE;EACb,CAAC;EAED;EACA/I,YAAY,EAAE;IACZkB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IACZnU,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdqR,eAAe,EAAE,oBAAoB;IACrCC,YAAY,EAAE,KAAK;IACnBrL,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBwC,cAAc,EAAE,QAAQ;IACxBsE,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BvG,MAAM,EAAE,MAAM;IACdwG,OAAO,EAAE,MAAM;IACfzG,SAAS,EAAE,+BAA+B;IAC1CF,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCR,MAAM,EAAE;EACV,CAAC;EAED;EACAjB,wBAAwB,EAAE;IACxBc,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,MAAM;IACXoD,IAAI,EAAE,MAAM;IAAE;IACdjO,OAAO,EAAE,MAAM;IACfiL,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,KAAK;IACVH,MAAM,EAAE,EAAE;IACVK,eAAe,EAAE,iBAAiB;IAAE;IACpCD,OAAO,EAAE,GAAG;IACZE,YAAY,EAAE,GAAG;IACjBG,SAAS,EAAE;EACb,CAAC;EACDxB,gBAAgB,EAAE;IAChBlQ,KAAK,EAAE,MAAM;IAAE;IACfC,MAAM,EAAE,MAAM;IACd2T,SAAS,EAAE,SAAS;IACpB1N,OAAO,EAAE,cAAc;IACvBiV,aAAa,EAAE;EACjB,CAAC;EACDhL,gBAAgB,EAAE;IAChBjK,OAAO,EAAE,MAAM;IACfgL,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,YAAY;IACxByI,UAAU,EAAE,GAAG,CAAE;EACnB,CAAC;EACDxJ,iBAAiB,EAAE;IACjB+C,QAAQ,EAAE,MAAM;IAAE;IAClB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfhL,OAAO,EAAE,IAAI;IACbwQ,aAAa,EAAE,QAAQ;IACvBE,YAAY,EAAE,KAAK,CAAE;EACvB,CAAC;EACDxI,gBAAgB,EAAE;IAChB6C,QAAQ,EAAE,MAAM;IAAE;IAClB9C,KAAK,EAAE,OAAO;IACd+C,UAAU,EAAE,GAAG;IACfwF,aAAa,EAAE,QAAQ;IACvBD,SAAS,EAAE;EACb;AACF,CAAC;AAED,eAAehe,YAAY;AAAC,IAAAgd,EAAA;AAAA6D,YAAA,CAAA7D,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}