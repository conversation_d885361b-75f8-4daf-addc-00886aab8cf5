{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\client\\\\ClientDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport EmbedCodeGenerator from '../../components/EmbedCodeGenerator';\nimport { motion } from 'framer-motion';\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\nimport { Eye, TrendingUp, Users, Clock, Code, Globe, Smartphone } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientDashboard = () => {\n  _s();\n  var _analyticsData$trends, _analyticsData$produc, _analyticsData$device;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n  const [showEmbedModal, setShowEmbedModal] = useState(false);\n  const [clientData, setClientData] = useState({\n    id: '',\n    totalTryOns: 0,\n    avgDuration: 0,\n    uniqueUsers: 0,\n    companyName: '',\n    email: '',\n    productType: 'watches',\n    productName: '',\n    productImageUrl: '',\n    caseDimensions: '42'\n  });\n  const [user, setUser] = useState(null);\n  const [analyticsData, setAnalyticsData] = useState({\n    trends: [],\n    products: [],\n    devices: [],\n    overview: null,\n    recentActivity: []\n  });\n  const [loading, setLoading] = useState(true);\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Load client data from localStorage and API\n  useEffect(() => {\n    const loadClientData = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n\n        // Fetch client profile\n        const profileResponse = await axios.get(`${apiUrl}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (profileResponse.data) {\n          setClientData(prev => ({\n            ...prev,\n            companyName: profileResponse.data.companyName || '',\n            email: profileResponse.data.email || ''\n          }));\n        }\n\n        // Calculate date range based on timeRange\n        const end = new Date();\n        let start = new Date();\n        switch (timeRange) {\n          case '7d':\n            start.setDate(start.getDate() - 7);\n            break;\n          case '30d':\n            start.setDate(start.getDate() - 30);\n            break;\n          case '90d':\n            start.setDate(start.getDate() - 90);\n            break;\n          case '1y':\n            start.setFullYear(start.getFullYear() - 1);\n            break;\n          default:\n            start.setDate(start.getDate() - 7);\n        }\n\n        // Fetch analytics data from multiple endpoints\n        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([axios.get(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }), axios.get(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        }), axios.get(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        })]);\n        if (timeAnalysisResponse.data) {\n          var _timeData$dailyTrends, _timeData$dailyTrends2, _timeData$dailyTrends3;\n          const timeData = timeAnalysisResponse.data;\n          const totalSessions = ((_timeData$dailyTrends = timeData.dailyTrends) === null || _timeData$dailyTrends === void 0 ? void 0 : _timeData$dailyTrends.reduce((sum, day) => sum + (day.sessions || 0), 0)) || 0;\n          const avgDuration = ((_timeData$dailyTrends2 = timeData.dailyTrends) === null || _timeData$dailyTrends2 === void 0 ? void 0 : _timeData$dailyTrends2.reduce((sum, day) => sum + (day.avgDuration || 0), 0)) / (((_timeData$dailyTrends3 = timeData.dailyTrends) === null || _timeData$dailyTrends3 === void 0 ? void 0 : _timeData$dailyTrends3.length) || 1);\n          setClientData(prev => ({\n            ...prev,\n            totalTryOns: totalSessions,\n            avgDuration: Math.round(avgDuration)\n          }));\n          setAnalyticsData(prev => ({\n            ...prev,\n            trends: timeData.dailyTrends || []\n          }));\n        }\n        if (productPerformanceResponse.data) {\n          setAnalyticsData(prev => ({\n            ...prev,\n            products: productPerformanceResponse.data\n          }));\n        }\n        if (deviceStatsResponse.data) {\n          const uniqueUsers = deviceStatsResponse.data.reduce((sum, device) => sum + (device.sessions || 0), 0);\n          setClientData(prev => ({\n            ...prev,\n            uniqueUsers\n          }));\n          setAnalyticsData(prev => ({\n            ...prev,\n            devices: deviceStatsResponse.data\n          }));\n        }\n      } catch (error) {\n        console.error('Error loading client data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadClientData();\n  }, [timeRange]);\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Format data for charts\n  const tryOnTrends = ((_analyticsData$trends = analyticsData.trends) === null || _analyticsData$trends === void 0 ? void 0 : _analyticsData$trends.map(trend => ({\n    date: trend._id,\n    tryOns: trend.sessions,\n    avgDuration: Math.round(trend.avgDuration || 0)\n  }))) || [];\n  const productPerformance = ((_analyticsData$produc = analyticsData.products) === null || _analyticsData$produc === void 0 ? void 0 : _analyticsData$produc.slice(0, 5).map(product => ({\n    name: product.productName || 'Unknown Product',\n    tryOns: product.sessions,\n    avgDuration: Math.round(product.avgDuration || 0)\n  }))) || [];\n  const deviceStats = ((_analyticsData$device = analyticsData.devices) === null || _analyticsData$device === void 0 ? void 0 : _analyticsData$device.map((device, index) => ({\n    name: device._id,\n    value: device.sessions,\n    color: ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 5]\n  }))) || [];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n        isOpen: isSidebarOpen,\n        onClose: () => setIsSidebarOpen(false),\n        collapsed: collapsed,\n        setCollapsed: setCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n        toggleSidebar: toggleSidebar,\n        collapsed: collapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `${mainMargin} pt-20 transition-all duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 md:p-6 space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 bg-gray-200 rounded w-1/3 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/2 mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-6\",\n              children: [1, 2, 3].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-8 bg-gray-200 rounded w-1/2 mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-1/4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n              children: [1, 2].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-80 bg-gray-200 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Virtual Try-On Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Monitor your product performance and customer engagement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 md:mt-0 flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n              children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setTimeRange(range),\n                className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n                children: range\n              }, range, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowEmbedModal(true),\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n              children: [/*#__PURE__*/_jsxDEV(Code, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), \"Get Embed Code\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.totalTryOns.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Avg Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [Math.floor(clientData.avgDuration / 60), \"m \", clientData.avgDuration % 60, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.5\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Try-On Trends\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64 md:h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: tryOnTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"tryOns\",\n                    stroke: \"#2D8C88\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#2D8C88'\n                    },\n                    name: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"avgDuration\",\n                    stroke: \"#3B82F6\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#3B82F6'\n                    },\n                    name: \"Avg Duration (s)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Device Usage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64 md:h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: deviceStats,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    children: deviceStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: entry.color\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.7\n          },\n          className: \"bg-white rounded-xl shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Top Performing Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64 md:h-80\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: productPerformance,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"tryOns\",\n                  fill: \"#2D8C88\",\n                  name: \"Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"avgDuration\",\n                  fill: \"#3B82F6\",\n                  name: \"Avg Duration (s)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium\",\n                children: \"Ready to integrate Virtual Try-On?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#2D8C88]/80 mt-1\",\n                children: \"Add our try-on button to your product pages in minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                children: \"View Guide\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\",\n                children: \"Get Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EmbedCodeGenerator, {\n      isOpen: showEmbedModal,\n      onClose: () => setShowEmbedModal(false),\n      clientData: {\n        ...clientData,\n        id: (user === null || user === void 0 ? void 0 : user.id) || (user === null || user === void 0 ? void 0 : user._id),\n        productType: (user === null || user === void 0 ? void 0 : user.productType) || 'watches'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientDashboard, \"J262ujV9ZZmOxrPdXRMj9lJkIVA=\");\n_c = ClientDashboard;\nexport default ClientDashboard;\nvar _c;\n$RefreshReg$(_c, \"ClientDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ClientSidebar", "ClientNavbar", "EmbedCodeGenerator", "motion", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Eye", "TrendingUp", "Users", "Clock", "Code", "Globe", "Smartphone", "axios", "jsxDEV", "_jsxDEV", "ClientDashboard", "_s", "_analyticsData$trends", "_analyticsData$produc", "_analyticsData$device", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "timeRange", "setTimeRange", "showEmbedModal", "setShowEmbedModal", "clientData", "setClientData", "id", "totalTryOns", "avgDuration", "uniqueUsers", "companyName", "email", "productType", "productName", "productImageUrl", "caseDimensions", "user", "setUser", "analyticsData", "setAnalyticsData", "trends", "products", "devices", "overview", "recentActivity", "loading", "setLoading", "toggleSidebar", "loadClientData", "token", "localStorage", "getItem", "Error", "baseUrl", "process", "env", "REACT_APP_API_URL", "apiUrl", "endsWith", "slice", "profileResponse", "get", "headers", "data", "prev", "end", "Date", "start", "setDate", "getDate", "setFullYear", "getFullYear", "timeAnalysisResponse", "productPerformanceResponse", "deviceStatsResponse", "Promise", "all", "toISOString", "_timeData$dailyTrends", "_timeData$dailyTrends2", "_timeData$dailyTrends3", "timeData", "totalSessions", "dailyTrends", "reduce", "sum", "day", "sessions", "length", "Math", "round", "device", "error", "console", "<PERSON><PERSON><PERSON><PERSON>", "tryOnTrends", "map", "trend", "date", "_id", "tryOns", "productPerformance", "product", "name", "deviceStats", "index", "value", "color", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "i", "range", "onClick", "div", "initial", "opacity", "y", "animate", "toLocaleString", "transition", "delay", "floor", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "toFixed", "entry", "_c", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/client/ClientDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Client<PERSON>idebar from '../../components/client/ClientSidebar';\r\nimport ClientNavbar from '../../components/client/ClientNavbar';\r\nimport EmbedCodeGenerator from '../../components/EmbedCodeGenerator';\r\nimport { motion } from 'framer-motion';\r\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\r\nimport { Eye, TrendingUp, Users, Clock, Code, Globe, Smartphone } from 'lucide-react';\r\nimport axios from 'axios';\r\n\r\nconst ClientDashboard = () => {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [timeRange, setTimeRange] = useState('7d');\r\n  const [showEmbedModal, setShowEmbedModal] = useState(false);\r\n  const [clientData, setClientData] = useState({\r\n    id: '',\r\n    totalTryOns: 0,\r\n    avgDuration: 0,\r\n    uniqueUsers: 0,\r\n    companyName: '',\r\n    email: '',\r\n    productType: 'watches',\r\n    productName: '',\r\n    productImageUrl: '',\r\n    caseDimensions: '42'\r\n  });\r\n  const [user, setUser] = useState(null);\r\n  const [analyticsData, setAnalyticsData] = useState({\r\n    trends: [],\r\n    products: [],\r\n    devices: [],\r\n    overview: null,\r\n    recentActivity: []\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const toggleSidebar = () => {\r\n    setIsSidebarOpen(!isSidebarOpen);\r\n  };\r\n\r\n  // Load client data from localStorage and API\r\n  useEffect(() => {\r\n    const loadClientData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const token = localStorage.getItem('token');\r\n\r\n        if (!token) {\r\n          throw new Error('No authentication token found');\r\n        }\r\n\r\n        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\r\n\r\n        // Fetch client profile\r\n        const profileResponse = await axios.get(`${apiUrl}/api/auth/me`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        });\r\n\r\n        if (profileResponse.data) {\r\n          setClientData(prev => ({\r\n            ...prev,\r\n            companyName: profileResponse.data.companyName || '',\r\n            email: profileResponse.data.email || ''\r\n          }));\r\n        }\r\n\r\n        // Calculate date range based on timeRange\r\n        const end = new Date();\r\n        let start = new Date();\r\n        switch (timeRange) {\r\n          case '7d':\r\n            start.setDate(start.getDate() - 7);\r\n            break;\r\n          case '30d':\r\n            start.setDate(start.getDate() - 30);\r\n            break;\r\n          case '90d':\r\n            start.setDate(start.getDate() - 90);\r\n            break;\r\n          case '1y':\r\n            start.setFullYear(start.getFullYear() - 1);\r\n            break;\r\n          default:\r\n            start.setDate(start.getDate() - 7);\r\n        }\r\n\r\n        // Fetch analytics data from multiple endpoints\r\n        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([\r\n          axios.get(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          }),\r\n          axios.get(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          }),\r\n          axios.get(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          })\r\n        ]);\r\n\r\n        if (timeAnalysisResponse.data) {\r\n          const timeData = timeAnalysisResponse.data;\r\n          const totalSessions = timeData.dailyTrends?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0;\r\n          const avgDuration = timeData.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / \r\n            (timeData.dailyTrends?.length || 1);\r\n\r\n          setClientData(prev => ({\r\n            ...prev,\r\n            totalTryOns: totalSessions,\r\n            avgDuration: Math.round(avgDuration)\r\n          }));\r\n\r\n          setAnalyticsData(prev => ({\r\n            ...prev,\r\n            trends: timeData.dailyTrends || []\r\n          }));\r\n        }\r\n\r\n        if (productPerformanceResponse.data) {\r\n          setAnalyticsData(prev => ({\r\n            ...prev,\r\n            products: productPerformanceResponse.data\r\n          }));\r\n        }\r\n\r\n        if (deviceStatsResponse.data) {\r\n          const uniqueUsers = deviceStatsResponse.data.reduce((sum, device) => sum + (device.sessions || 0), 0);\r\n          setClientData(prev => ({\r\n            ...prev,\r\n            uniqueUsers\r\n          }));\r\n          setAnalyticsData(prev => ({\r\n            ...prev,\r\n            devices: deviceStatsResponse.data\r\n          }));\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('Error loading client data:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadClientData();\r\n  }, [timeRange]);\r\n\r\n  // Calculate margin for main content\r\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\r\n\r\n  // Format data for charts\r\n  const tryOnTrends = analyticsData.trends?.map(trend => ({\r\n    date: trend._id,\r\n    tryOns: trend.sessions,\r\n    avgDuration: Math.round(trend.avgDuration || 0)\r\n  })) || [];\r\n\r\n  const productPerformance = analyticsData.products?.slice(0, 5).map(product => ({\r\n    name: product.productName || 'Unknown Product',\r\n    tryOns: product.sessions,\r\n    avgDuration: Math.round(product.avgDuration || 0)\r\n  })) || [];\r\n\r\n  const deviceStats = analyticsData.devices?.map((device, index) => ({\r\n    name: device._id,\r\n    value: device.sessions,\r\n    color: ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 5]\r\n  })) || [];\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\r\n        <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\r\n\r\n        <main className={`${mainMargin} pt-20 transition-all duration-300`}>\r\n          <div className=\"p-4 md:p-6 space-y-6\">\r\n            <div className=\"animate-pulse\">\r\n              <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-2\"></div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-6\"></div>\r\n\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-6\">\r\n                {[1, 2, 3].map((i) => (\r\n                  <div key={i} className=\"bg-white rounded-xl shadow-sm p-6\">\r\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\r\n                    <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-4\"></div>\r\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\">\r\n                {[1, 2].map((i) => (\r\n                  <div key={i} className=\"bg-white rounded-xl shadow-sm p-6\">\r\n                    <div className=\"h-6 bg-gray-200 rounded w-1/4 mb-4\"></div>\r\n                    <div className=\"h-80 bg-gray-200 rounded\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\r\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\r\n\r\n      {/* Main Content */}\r\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\r\n        <div className=\"p-4 md:p-6 space-y-6\">\r\n          {/* Page Header */}\r\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">Virtual Try-On Dashboard</h1>\r\n              <p className=\"text-gray-600\">Monitor your product performance and customer engagement</p>\r\n            </div>\r\n            <div className=\"mt-4 md:mt-0 flex space-x-3\">\r\n              <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\r\n                {['7d', '30d', '90d', '1y'].map((range) => (\r\n                  <button\r\n                    key={range}\r\n                    onClick={() => setTimeRange(range)}\r\n                    className={`px-3 py-1 text-sm font-medium rounded-md ${\r\n                      timeRange === range\r\n                        ? 'bg-[#2D8C88] text-white'\r\n                        : 'text-gray-600 hover:text-gray-900'\r\n                    }`}\r\n                  >\r\n                    {range}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              <button\r\n                onClick={() => setShowEmbedModal(true)}\r\n                className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\r\n              >\r\n                <Code className=\"h-4 w-4 mr-2\" />\r\n                Get Embed Code\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Enhanced Stats Grid */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6\">\r\n            {/* Total Try-Ons */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.totalTryOns.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\r\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Average Duration */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Avg Duration</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{Math.floor(clientData.avgDuration / 60)}m {clientData.avgDuration % 60}s</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\r\n                  <Clock className=\"h-6 w-6 text-blue-500\" />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Unique Users */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.uniqueUsers.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\r\n                  <Users className=\"h-6 w-6 text-purple-500\" />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Charts Grid */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\">\r\n            {/* Try-On Trends */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.5 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Try-On Trends</h3>\r\n              <div className=\"h-64 md:h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={tryOnTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"date\" />\r\n                    <YAxis />\r\n                    <Tooltip />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"tryOns\"\r\n                      stroke=\"#2D8C88\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#2D8C88' }}\r\n                      name=\"Try-Ons\"\r\n                    />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"avgDuration\"\r\n                      stroke=\"#3B82F6\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#3B82F6' }}\r\n                      name=\"Avg Duration (s)\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Device Distribution */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.6 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Usage</h3>\r\n              <div className=\"h-64 md:h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <PieChart>\r\n                    <Pie\r\n                      data={deviceStats}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      labelLine={false}\r\n                      outerRadius={80}\r\n                      fill=\"#8884d8\"\r\n                      dataKey=\"value\"\r\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                    >\r\n                      {deviceStats.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={entry.color} />\r\n                      ))}\r\n                    </Pie>\r\n                    <Tooltip />\r\n                  </PieChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Product Performance */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.7 }}\r\n            className=\"bg-white rounded-xl shadow-sm p-6\"\r\n          >\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Performing Products</h3>\r\n            <div className=\"h-64 md:h-80\">\r\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                <BarChart data={productPerformance}>\r\n                  <CartesianGrid strokeDasharray=\"3 3\" />\r\n                  <XAxis dataKey=\"name\" />\r\n                  <YAxis />\r\n                  <Tooltip />\r\n                  <Bar dataKey=\"tryOns\" fill=\"#2D8C88\" name=\"Try-Ons\" />\r\n                  <Bar dataKey=\"avgDuration\" fill=\"#3B82F6\" name=\"Avg Duration (s)\" />\r\n                </BarChart>\r\n              </ResponsiveContainer>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Integration Guide */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.8 }}\r\n            className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6\"\r\n          >\r\n            <div className=\"flex items-center justify-between text-white\">\r\n              <div>\r\n                <h3 className=\"text-lg font-medium\">Ready to integrate Virtual Try-On?</h3>\r\n                <p className=\"text-[#2D8C88]/80 mt-1\">Add our try-on button to your product pages in minutes</p>\r\n              </div>\r\n              <div className=\"flex space-x-3\">\r\n                <button className=\"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\">\r\n                  View Guide\r\n                </button>\r\n                <button className=\"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\">\r\n                  Get Code\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Debug: Backend Test */}\r\n        </div>\r\n      </main>\r\n\r\n      {/* Embed Code Modal */}\r\n      <EmbedCodeGenerator\r\n        isOpen={showEmbedModal}\r\n        onClose={() => setShowEmbedModal(false)}\r\n        clientData={{\r\n          ...clientData,\r\n          id: user?.id || user?._id,\r\n          productType: user?.productType || 'watches'\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClientDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACzI,SAASC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AACrF,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC5B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC;IAC3C2C,EAAE,EAAE,EAAE;IACNC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC;IACjDyD,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMgE,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,MAAMgE,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFF,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;QAClD;QAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGN,OAAO;;QAErE;QACA,MAAMO,eAAe,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CAAC,GAAGJ,MAAM,cAAc,EAAE;UAC/DK,OAAO,EAAE;YACP,eAAe,EAAE,UAAUb,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIW,eAAe,CAACG,IAAI,EAAE;UACxBtC,aAAa,CAACuC,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPlC,WAAW,EAAE8B,eAAe,CAACG,IAAI,CAACjC,WAAW,IAAI,EAAE;YACnDC,KAAK,EAAE6B,eAAe,CAACG,IAAI,CAAChC,KAAK,IAAI;UACvC,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,MAAMkC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,IAAIC,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,QAAQ9C,SAAS;UACf,KAAK,IAAI;YACP+C,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,KAAK;YACRF,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;YACnC;UACF,KAAK,IAAI;YACPF,KAAK,CAACG,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1C;UACF;YACEJ,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC;;QAEA;QACA,MAAM,CAACG,oBAAoB,EAAEC,0BAA0B,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChGpE,KAAK,CAACqD,GAAG,CAAC,GAAGJ,MAAM,6CAA6CU,KAAK,CAACU,WAAW,CAAC,CAAC,QAAQZ,GAAG,CAACY,WAAW,CAAC,CAAC,EAAE,EAAE;UAC9Gf,OAAO,EAAE;YACP,eAAe,EAAE,UAAUb,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,EACFzC,KAAK,CAACqD,GAAG,CAAC,GAAGJ,MAAM,mDAAmDU,KAAK,CAACU,WAAW,CAAC,CAAC,QAAQZ,GAAG,CAACY,WAAW,CAAC,CAAC,EAAE,EAAE;UACpHf,OAAO,EAAE;YACP,eAAe,EAAE,UAAUb,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,EACFzC,KAAK,CAACqD,GAAG,CAAC,GAAGJ,MAAM,4CAA4CU,KAAK,CAACU,WAAW,CAAC,CAAC,QAAQZ,GAAG,CAACY,WAAW,CAAC,CAAC,EAAE,EAAE;UAC7Gf,OAAO,EAAE;YACP,eAAe,EAAE,UAAUb,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC,CACH,CAAC;QAEF,IAAIuB,oBAAoB,CAACT,IAAI,EAAE;UAAA,IAAAe,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAC7B,MAAMC,QAAQ,GAAGT,oBAAoB,CAACT,IAAI;UAC1C,MAAMmB,aAAa,GAAG,EAAAJ,qBAAA,GAAAG,QAAQ,CAACE,WAAW,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,CAAC;UACnG,MAAM3D,WAAW,GAAG,EAAAmD,sBAAA,GAAAE,QAAQ,CAACE,WAAW,cAAAJ,sBAAA,uBAApBA,sBAAA,CAAsBK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAAC1D,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,KAC5F,EAAAoD,sBAAA,GAAAC,QAAQ,CAACE,WAAW,cAAAH,sBAAA,uBAApBA,sBAAA,CAAsBQ,MAAM,KAAI,CAAC,CAAC;UAErC/D,aAAa,CAACuC,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPrC,WAAW,EAAEuD,aAAa;YAC1BtD,WAAW,EAAE6D,IAAI,CAACC,KAAK,CAAC9D,WAAW;UACrC,CAAC,CAAC,CAAC;UAEHW,gBAAgB,CAACyB,IAAI,KAAK;YACxB,GAAGA,IAAI;YACPxB,MAAM,EAAEyC,QAAQ,CAACE,WAAW,IAAI;UAClC,CAAC,CAAC,CAAC;QACL;QAEA,IAAIV,0BAA0B,CAACV,IAAI,EAAE;UACnCxB,gBAAgB,CAACyB,IAAI,KAAK;YACxB,GAAGA,IAAI;YACPvB,QAAQ,EAAEgC,0BAA0B,CAACV;UACvC,CAAC,CAAC,CAAC;QACL;QAEA,IAAIW,mBAAmB,CAACX,IAAI,EAAE;UAC5B,MAAMlC,WAAW,GAAG6C,mBAAmB,CAACX,IAAI,CAACqB,MAAM,CAAC,CAACC,GAAG,EAAEM,MAAM,KAAKN,GAAG,IAAIM,MAAM,CAACJ,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UACrG9D,aAAa,CAACuC,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPnC;UACF,CAAC,CAAC,CAAC;UACHU,gBAAgB,CAACyB,IAAI,KAAK;YACxB,GAAGA,IAAI;YACPtB,OAAO,EAAEgC,mBAAmB,CAACX;UAC/B,CAAC,CAAC,CAAC;QACL;MAEF,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACR9C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5B,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM0E,UAAU,GAAG5E,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAM6E,WAAW,GAAG,EAAAlF,qBAAA,GAAAyB,aAAa,CAACE,MAAM,cAAA3B,qBAAA,uBAApBA,qBAAA,CAAsBmF,GAAG,CAACC,KAAK,KAAK;IACtDC,IAAI,EAAED,KAAK,CAACE,GAAG;IACfC,MAAM,EAAEH,KAAK,CAACV,QAAQ;IACtB3D,WAAW,EAAE6D,IAAI,CAACC,KAAK,CAACO,KAAK,CAACrE,WAAW,IAAI,CAAC;EAChD,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMyE,kBAAkB,GAAG,EAAAvF,qBAAA,GAAAwB,aAAa,CAACG,QAAQ,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAwB6C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACqC,GAAG,CAACM,OAAO,KAAK;IAC7EC,IAAI,EAAED,OAAO,CAACrE,WAAW,IAAI,iBAAiB;IAC9CmE,MAAM,EAAEE,OAAO,CAACf,QAAQ;IACxB3D,WAAW,EAAE6D,IAAI,CAACC,KAAK,CAACY,OAAO,CAAC1E,WAAW,IAAI,CAAC;EAClD,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAM4E,WAAW,GAAG,EAAAzF,qBAAA,GAAAuB,aAAa,CAACI,OAAO,cAAA3B,qBAAA,uBAArBA,qBAAA,CAAuBiF,GAAG,CAAC,CAACL,MAAM,EAAEc,KAAK,MAAM;IACjEF,IAAI,EAAEZ,MAAM,CAACQ,GAAG;IAChBO,KAAK,EAAEf,MAAM,CAACJ,QAAQ;IACtBoB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACF,KAAK,GAAG,CAAC;EAC1E,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,IAAI5D,OAAO,EAAE;IACX,oBACEnC,OAAA;MAAKkG,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCnG,OAAA,CAACzB,aAAa;QAAC6H,MAAM,EAAE9F,aAAc;QAAC+F,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;QAACC,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClIzG,OAAA,CAACxB,YAAY;QAAC6D,aAAa,EAAEA,aAAc;QAAC7B,SAAS,EAAEA;MAAU;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpEzG,OAAA;QAAMkG,SAAS,EAAE,GAAGd,UAAU,oCAAqC;QAAAe,QAAA,eACjEnG,OAAA;UAAKkG,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCnG,OAAA;YAAKkG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnG,OAAA;cAAKkG,SAAS,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DzG,OAAA;cAAKkG,SAAS,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE1DzG,OAAA;cAAKkG,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAChF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAEoB,CAAC,iBACf1G,OAAA;gBAAakG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACxDnG,OAAA;kBAAKkG,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DzG,OAAA;kBAAKkG,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DzG,OAAA;kBAAKkG,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAH7CC,CAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzG,OAAA;cAAKkG,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAC5D,CAAC,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAEoB,CAAC,iBACZ1G,OAAA;gBAAakG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACxDnG,OAAA;kBAAKkG,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DzG,OAAA;kBAAKkG,SAAS,EAAC;gBAA0B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFxCC,CAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEzG,OAAA;IAAKkG,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCnG,OAAA,CAACzB,aAAa;MAAC6H,MAAM,EAAE9F,aAAc;MAAC+F,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClIzG,OAAA,CAACxB,YAAY;MAAC6D,aAAa,EAAEA,aAAc;MAAC7B,SAAS,EAAEA;IAAU;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpEzG,OAAA;MAAMkG,SAAS,EAAE,GAAGd,UAAU,oCAAqC;MAAAe,QAAA,eACjEnG,OAAA;QAAKkG,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEnCnG,OAAA;UAAKkG,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFnG,OAAA;YAAAmG,QAAA,gBACEnG,OAAA;cAAIkG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EzG,OAAA;cAAGkG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNzG,OAAA;YAAKkG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CnG,OAAA;cAAKkG,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACb,GAAG,CAAEqB,KAAK,iBACpC3G,OAAA;gBAEE4G,OAAO,EAAEA,CAAA,KAAMjG,YAAY,CAACgG,KAAK,CAAE;gBACnCT,SAAS,EAAE,4CACTxF,SAAS,KAAKiG,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;gBAAAR,QAAA,EAEFQ;cAAK,GARDA,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzG,OAAA;cACE4G,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,IAAI,CAAE;cACvCqF,SAAS,EAAC,6KAA6K;cAAAC,QAAA,gBAEvLnG,OAAA,CAACL,IAAI;gBAACuG,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzG,OAAA;UAAKkG,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAE5EnG,OAAA,CAACtB,MAAM,CAACmI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9Bd,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CnG,OAAA;cAAKkG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnG,OAAA;gBAAAmG,QAAA,gBACEnG,OAAA;kBAAGkG,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEzG,OAAA;kBAAGkG,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAErF,UAAU,CAACG,WAAW,CAACiG,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNzG,OAAA;gBAAKkG,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFnG,OAAA,CAACT,GAAG;kBAAC2G,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzG,OAAA,CAACtB,MAAM,CAACmI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CnG,OAAA;cAAKkG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnG,OAAA;gBAAAmG,QAAA,gBACEnG,OAAA;kBAAGkG,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEzG,OAAA;kBAAGkG,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAEpB,IAAI,CAACsC,KAAK,CAACvG,UAAU,CAACI,WAAW,GAAG,EAAE,CAAC,EAAC,IAAE,EAACJ,UAAU,CAACI,WAAW,GAAG,EAAE,EAAC,GAAC;gBAAA;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC,eACNzG,OAAA;gBAAKkG,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFnG,OAAA,CAACN,KAAK;kBAACwG,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzG,OAAA,CAACtB,MAAM,CAACmI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CnG,OAAA;cAAKkG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnG,OAAA;gBAAAmG,QAAA,gBACEnG,OAAA;kBAAGkG,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjEzG,OAAA;kBAAGkG,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAErF,UAAU,CAACK,WAAW,CAAC+F,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNzG,OAAA;gBAAKkG,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFnG,OAAA,CAACP,KAAK;kBAACyG,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNzG,OAAA;UAAKkG,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAE7DnG,OAAA,CAACtB,MAAM,CAACmI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnG,OAAA;cAAIkG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEzG,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BnG,OAAA,CAACb,mBAAmB;gBAACmI,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAApB,QAAA,eAC7CnG,OAAA,CAACrB,SAAS;kBAAC0E,IAAI,EAAEgC,WAAY;kBAAAc,QAAA,gBAC3BnG,OAAA,CAACf,aAAa;oBAACuI,eAAe,EAAC;kBAAK;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCzG,OAAA,CAACjB,KAAK;oBAAC0I,OAAO,EAAC;kBAAM;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBzG,OAAA,CAAChB,KAAK;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTzG,OAAA,CAACd,OAAO;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXzG,OAAA,CAACpB,IAAI;oBACH8I,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,QAAQ;oBAChBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBjC,IAAI,EAAC;kBAAS;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFzG,OAAA,CAACpB,IAAI;oBACH8I,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,aAAa;oBACrBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBjC,IAAI,EAAC;kBAAkB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbzG,OAAA,CAACtB,MAAM,CAACmI,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BlB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7CnG,OAAA;cAAIkG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEzG,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BnG,OAAA,CAACb,mBAAmB;gBAACmI,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAApB,QAAA,eAC7CnG,OAAA,CAACZ,QAAQ;kBAAA+G,QAAA,gBACPnG,OAAA,CAACX,GAAG;oBACFgE,IAAI,EAAEyC,WAAY;oBAClBiC,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBC,WAAW,EAAE,EAAG;oBAChBJ,IAAI,EAAC,SAAS;oBACdL,OAAO,EAAC,OAAO;oBACfU,KAAK,EAAEA,CAAC;sBAAEtC,IAAI;sBAAEuC;oBAAQ,CAAC,KAAK,GAAGvC,IAAI,IAAI,CAACuC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;oBAAAlC,QAAA,EAEtEL,WAAW,CAACR,GAAG,CAAC,CAACgD,KAAK,EAAEvC,KAAK,kBAC5B/F,OAAA,CAACV,IAAI;sBAAuBwI,IAAI,EAAEQ,KAAK,CAACrC;oBAAM,GAAnC,QAAQF,KAAK,EAAE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAsB,CACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNzG,OAAA,CAACd,OAAO;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNzG,OAAA,CAACtB,MAAM,CAACmI,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7CnG,OAAA;YAAIkG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFzG,OAAA;YAAKkG,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BnG,OAAA,CAACb,mBAAmB;cAACmI,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAApB,QAAA,eAC7CnG,OAAA,CAACnB,QAAQ;gBAACwE,IAAI,EAAEsC,kBAAmB;gBAAAQ,QAAA,gBACjCnG,OAAA,CAACf,aAAa;kBAACuI,eAAe,EAAC;gBAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCzG,OAAA,CAACjB,KAAK;kBAAC0I,OAAO,EAAC;gBAAM;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBzG,OAAA,CAAChB,KAAK;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTzG,OAAA,CAACd,OAAO;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXzG,OAAA,CAAClB,GAAG;kBAAC2I,OAAO,EAAC,QAAQ;kBAACK,IAAI,EAAC,SAAS;kBAACjC,IAAI,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtDzG,OAAA,CAAClB,GAAG;kBAAC2I,OAAO,EAAC,aAAa;kBAACK,IAAI,EAAC,SAAS;kBAACjC,IAAI,EAAC;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbzG,OAAA,CAACtB,MAAM,CAACmI,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BlB,SAAS,EAAC,uEAAuE;UAAAC,QAAA,eAEjFnG,OAAA;YAAKkG,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAC3DnG,OAAA;cAAAmG,QAAA,gBACEnG,OAAA;gBAAIkG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3EzG,OAAA;gBAAGkG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNzG,OAAA;cAAKkG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnG,OAAA;gBAAQkG,SAAS,EAAC,6FAA6F;gBAAAC,QAAA,EAAC;cAEhH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzG,OAAA;gBAAQkG,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAElH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPzG,OAAA,CAACvB,kBAAkB;MACjB2H,MAAM,EAAExF,cAAe;MACvByF,OAAO,EAAEA,CAAA,KAAMxF,iBAAiB,CAAC,KAAK,CAAE;MACxCC,UAAU,EAAE;QACV,GAAGA,UAAU;QACbE,EAAE,EAAE,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEV,EAAE,MAAIU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,GAAG;QACzBnE,WAAW,EAAE,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEJ,WAAW,KAAI;MACpC;IAAE;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvG,EAAA,CApbID,eAAe;AAAAsI,EAAA,GAAftI,eAAe;AAsbrB,eAAeA,eAAe;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}